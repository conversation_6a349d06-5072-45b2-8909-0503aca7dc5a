# General Settings for ForexFactory Scraper
scraping:
  # Rate limiting settings
  delay_between_requests: 2.5  # seconds
  request_timeout: 30  # seconds
  
  # Retry settings
  max_retries: 3
  retry_delay: 1  # seconds (will use exponential backoff)
  
  # Browser settings
  headless: true
  window_size: [1920, 1080]
  
  # User agents for rotation (to avoid detection)
  user_agents:
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"
    - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

# Data validation settings
data_validation:
  required_fields: ["timestamp", "currency", "event", "impact", "actual", "forecast", "previous"]
  high_impact_only: true
  allowed_currencies: ["USD", "EUR", "GBP", "JPY", "CAD", "AUD", "NZD", "CHF"]
  
# Logging settings
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  log_to_file: true
  log_file: "logs/scraper.log"
  max_log_size_mb: 10
  backup_count: 5

# Output settings
output:
  format: "csv"  # csv or json
  include_timestamp_in_filename: true
  data_directory: "data/news/raw"
  
# ForexFactory specific settings
forexfactory:
  base_url: "https://www.forexfactory.com/calendar"
  timezone: "UTC"
  date_format: "%Y-%m-%d %H:%M:%S" 