#!/usr/bin/env python3
"""
Monitor batch scraping progress.
"""

import os
import yaml
from pathlib import Path
from datetime import datetime


def monitor_progress():
    """Monitor batch scraping progress."""
    
    # Check progress file
    progress_file = 'data/news/cache/batch_progress.yaml'
    
    if os.path.exists(progress_file):
        with open(progress_file, 'r') as f:
            progress = yaml.safe_load(f) or {}
        
        completed_weeks = progress.get('completed_weeks', [])
        last_updated = progress.get('last_updated', 'Unknown')
        
        print(f"📊 Batch Scraping Progress")
        print(f"{'='*50}")
        print(f"Completed weeks: {len(completed_weeks)}")
        print(f"Last updated: {last_updated}")
        print(f"Recent weeks: {completed_weeks[-5:] if len(completed_weeks) >= 5 else completed_weeks}")
    else:
        print("❌ No progress file found")
    
    # Check raw data files
    raw_dir = Path('data/news/raw')
    if raw_dir.exists():
        csv_files = list(raw_dir.glob('forexfactory_*.csv'))
        print(f"\n📁 Raw Data Files")
        print(f"{'='*50}")
        print(f"Total CSV files: {len(csv_files)}")
        
        if csv_files:
            # Show latest files
            latest_files = sorted(csv_files, key=lambda x: x.stat().st_mtime)[-3:]
            print(f"Latest files:")
            for file in latest_files:
                mod_time = datetime.fromtimestamp(file.stat().st_mtime)
                print(f"  - {file.name} ({mod_time.strftime('%Y-%m-%d %H:%M:%S')})")
    else:
        print("❌ No raw data directory found")
    
    # Check if consolidation exists
    processed_dir = Path('data/news/processed')
    if processed_dir.exists():
        consolidated_files = list(processed_dir.glob('consolidated_events_*.csv'))
        if consolidated_files:
            print(f"\n📈 Consolidated Data")
            print(f"{'='*50}")
            print(f"Consolidated files: {len(consolidated_files)}")
            for file in consolidated_files:
                mod_time = datetime.fromtimestamp(file.stat().st_mtime)
                print(f"  - {file.name} ({mod_time.strftime('%Y-%m-%d %H:%M:%S')})")


if __name__ == '__main__':
    monitor_progress() 