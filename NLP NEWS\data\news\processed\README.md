# Processed Economic Events Data

This directory contains the processed economic events data for the backtestable high-impact news trading strategy.

## Files Description

### 📊 **Core Data Files**

#### `economic_events_raw.csv`
- **Source**: Consolidated data from ForexFactory scraping
- **Content**: Raw economic events with basic cleaning applied
- **Columns**: datetime, currency, event, impact, actual, forecast, previous, timestamp
- **Records**: 452 economic events (6-month period)
- **Usage**: Input data for sentiment analysis

#### `economic_events_detailed.csv`
- **Source**: Enhanced version of raw data with additional metadata
- **Content**: Same as raw data with extra processing details
- **Usage**: Detailed analysis and debugging

#### `economic_events_labeled.csv` ⭐ **MAIN OUTPUT**
- **Source**: Sentiment-labeled version of raw data
- **Content**: Economic events with comprehensive sentiment analysis
- **Additional Columns**:
  - `usd_sentiment`: USD sentiment (bullish/bearish/neutral)
  - `gold_sentiment`: Gold sentiment (bullish/bearish/neutral)
  - `eur_sentiment`: EUR sentiment (bullish/bearish/neutral)
  - `gbp_sentiment`: GBP sentiment (bullish/bearish/neutral)
  - `sentiment_confidence`: Confidence score (0.0-1.0)
  - `sentiment_category`: Event category (employment, inflation, etc.)
  - `sentiment_method`: Classification method used
  - `sentiment_reason`: Human-readable explanation
  - `surprise_pct`: Surprise magnitude percentage
  - `surprise_direction`: Surprise direction (positive/negative/neutral)
- **Usage**: Primary input for backtesting trading strategies

### 📈 **Analysis Reports**

#### `sentiment_analysis_report.yaml`
- **Content**: Comprehensive summary of sentiment analysis results
- **Includes**:
  - Total events processed
  - Processing method breakdown
  - Sentiment distribution statistics
  - Confidence score statistics
  - Category-wise analysis
- **Usage**: Quality assessment and strategy validation

## Data Quality & Validation

### ✅ **Fixes Applied**
1. **Unemployment Rate Logic**: Proper inverse relationship handling
2. **Unemployment Claims Logic**: Correct interpretation (fewer claims = USD bullish)
3. **Interest Rates Logic**: Proper rate change vs expectation analysis
4. **PMI Logic**: 50-threshold expansion/contraction handling
5. **Inflation Logic**: Inverse relationship (lower inflation = USD bearish)

### 📊 **Statistics**
- **Total Events**: 452
- **Quantitative Events**: 342 (75.7%)
- **Non-Quantitative Events**: 110 (24.3%)
- **Sentiment Distribution**: Balanced across bullish/bearish/neutral

### 🎯 **Confidence Levels**
- **High Confidence (>0.8)**: Major indicators (NFP, CPI, Fed Rate)
- **Medium Confidence (0.5-0.8)**: Regional data, PMI
- **Low Confidence (<0.5)**: Speeches, preliminary data

## Usage in Trading Strategy

The `economic_events_labeled.csv` file is ready for backtesting with the following approach:

1. **Filter by Impact**: Focus on "High" impact events
2. **Use Confidence Scores**: Weight signals by confidence levels
3. **Consider Asset Correlations**: USD strength = Gold weakness
4. **Time-based Analysis**: Events are timestamped for precise entry timing

## Data Lineage

```
ForexFactory Scraping → Raw Events → Sentiment Analysis → Labeled Events
                                          ↓
                                   Trading Strategy
```

Last Updated: 2025-07-07
Data Period: January 2024 - June 2024
