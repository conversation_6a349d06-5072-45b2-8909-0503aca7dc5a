# Sentiment Classification Rules for Economic Events
# Phase 2: News Sentiment Labeling System

# =============================================================================
# QUANTITATIVE EVENTS - Rule-based sentiment using actual vs forecast deltas
# =============================================================================

quantitative_events:
  
  # Employment Indicators
  employment:
    events:
      - "Non-Farm Employment Change"
      - "ADP Non-Farm Employment Change" 
      - "Employment Change"
      - "Employment Change q/q"
      - "Unemployment Rate"
      - "JOLTS Job Openings"
      - "Unemployment Claims"
      - "Average Hourly Earnings m/m"
      - "Claimant Count Change"
    
    sentiment_rules:
      # Better employment = USD strength = Gold bearish
      stronger_than_expected:
        conditions: ["actual > forecast", "positive_surprise > 0.1%"]
        usd_sentiment: "bullish"
        gold_sentiment: "bearish"
        confidence: 0.9
        
      weaker_than_expected:
        conditions: ["actual < forecast", "negative_surprise > 0.1%"]
        usd_sentiment: "bearish" 
        gold_sentiment: "bullish"
        confidence: 0.9
        
      # Special case: Unemployment Rate (inverse relationship)
      unemployment_rate_higher:
        conditions: ["actual > forecast", "event == 'Unemployment Rate'"]
        usd_sentiment: "bearish"
        gold_sentiment: "bullish" 
        confidence: 0.9

  # Inflation Indicators  
  inflation:
    events:
      - "CPI m/m"
      - "CPI y/y" 
      - "Core CPI m/m"
      - "PPI m/m"
      - "Core PPI m/m"
      - "PCE Price Index m/m"
      - "Core PCE Price Index m/m"
      - "German Prelim CPI m/m"
      - "CPI q/q"
      - "Trimmed Mean CPI q/q"
      - "Median CPI y/y"
      - "Trimmed CPI y/y"
      - "Employment Cost Index q/q"
      - "Wage Price Index q/q"
      - "Core CPI Flash Estimate y/y"
      - "CPI Flash Estimate y/y"
    
    sentiment_rules:
      # Higher inflation = USD strength (rate hikes) = Gold bearish
      hotter_than_expected:
        conditions: ["actual > forecast", "positive_surprise > 0.05%"]
        usd_sentiment: "bullish"
        gold_sentiment: "bearish"
        confidence: 0.95
        
      cooler_than_expected:  
        conditions: ["actual < forecast", "negative_surprise > 0.05%"]
        usd_sentiment: "bearish"
        gold_sentiment: "bullish"
        confidence: 0.95

  # Economic Growth
  growth:
    events:
      - "GDP m/m"
      - "GDP q/q" 
      - "Prelim GDP q/q"
      - "Advance GDP q/q"
      - "Final GDP q/q"
      - "Retail Sales m/m"
      - "Core Retail Sales m/m"
      - "Pending Home Sales m/m"
      - "New Home Sales"
    
    sentiment_rules:
      stronger_growth:
        conditions: ["actual > forecast", "positive_surprise > 0.05%"]
        usd_sentiment: "bullish"
        gold_sentiment: "bearish"
        confidence: 0.8
        
      weaker_growth:
        conditions: ["actual < forecast", "negative_surprise > 0.05%"]
        usd_sentiment: "bearish"
        gold_sentiment: "bullish"
        confidence: 0.8

  # Manufacturing & Business Activity
  business_activity:
    events:
      - "ISM Manufacturing PMI"
      - "ISM Services PMI"
      - "Flash Manufacturing PMI"
      - "Flash Services PMI" 
      - "German Flash Manufacturing PMI"
      - "German Flash Services PMI"
      - "French Flash Manufacturing PMI"
      - "French Flash Services PMI"
      - "Final Manufacturing PMI"
      - "Empire State Manufacturing Index"
      - "Durable Goods Orders m/m"
    
    sentiment_rules:
      # IMPROVED: Better than expected (general improvement)
      stronger_than_expected:
        conditions: ["actual > forecast", "positive_surprise > 0.05"]
        usd_sentiment: "bullish"
        gold_sentiment: "bearish"
        confidence: 0.7
        
      weaker_than_expected:
        conditions: ["actual < forecast", "negative_surprise > 0.05"]
        usd_sentiment: "bearish"
        gold_sentiment: "bullish"
        confidence: 0.7
      
      # PMI > 50 = expansion, < 50 = contraction
      expansion_stronger:
        conditions: ["actual > forecast", "actual > 50", "positive_surprise > 0.05"]
        usd_sentiment: "bullish"
        gold_sentiment: "bearish"
        confidence: 0.8
        
      contraction_deeper:
        conditions: ["actual < forecast", "actual < 50", "negative_surprise > 0.05"]
        usd_sentiment: "bearish"
        gold_sentiment: "bullish" 
        confidence: 0.8

  # Consumer Indicators
  consumer:
    events:
      - "CB Consumer Confidence"
      - "Prelim UoM Consumer Sentiment"
      - "Revised UoM Consumer Sentiment"
      - "Inflation Expectations q/q"
    
    sentiment_rules:
      stronger_confidence:
        conditions: ["actual > forecast", "positive_surprise > 0.05"]
        usd_sentiment: "bullish"
        gold_sentiment: "bearish"
        confidence: 0.6
        
      weaker_confidence:
        conditions: ["actual < forecast", "negative_surprise > 0.05"]
        usd_sentiment: "bearish"
        gold_sentiment: "bullish"
        confidence: 0.6

  # Central Bank Rates
  interest_rates:
    events:
      - "Federal Funds Rate"
      - "Official Bank Rate"
      - "Cash Rate"
      - "Overnight Rate"
      - "Main Refinancing Rate"
      - "Official Cash Rate"
      - "BOJ Policy Rate"
      - "SNB Policy Rate"
    
    sentiment_rules:
      rate_hike:
        conditions: ["actual > previous", "rate_change > 0"]
        usd_sentiment: "bullish"
        gold_sentiment: "bearish"
        confidence: 1.0
        
      rate_cut:
        conditions: ["actual < previous", "rate_change < 0"] 
        usd_sentiment: "bearish"
        gold_sentiment: "bullish"
        confidence: 1.0

# =============================================================================
# NON-QUANTITATIVE EVENTS - Framework for future NLP integration
# =============================================================================

non_quantitative_events:
  
  # Central Bank Communications
  fed_communications:
    events:
      - "FOMC Statement"
      - "FOMC Press Conference" 
      - "FOMC Meeting Minutes"
      - "FOMC Economic Projections"
      - "Fed Chair Powell Speaks"
      - "Fed Chair Powell Testifies"
      - "FOMC Member Waller Speaks"
    
    processing_method: "nlp_required"
    sentiment_approach: "contextual_analysis"
    keywords:
      hawkish: ["inflation concerns", "rate hikes", "tightening", "restrictive", "above target"]
      dovish: ["accommodative", "supportive", "patient", "gradual", "data dependent"]
    
    confidence: 0.5  # Lower confidence until NLP is implemented
    
  # Other Central Bank Communications  
  other_cb_communications:
    events:
      - "BOE Gov Bailey Speaks"
      - "ECB Press Conference"
      - "BOC Press Conference"
      - "RBA Press Conference"
      - "BOJ Press Conference"
      - "SNB Press Conference"
      - "BOJ Gov Ueda Speaks"
      - "RBA Gov Bullock Speaks"
      - "BOC Gov Macklem Speaks"
    
    processing_method: "nlp_required"
    sentiment_approach: "contextual_analysis"
    confidence: 0.3  # Very low confidence without NLP

  # Policy Documents & Reports
  policy_documents:
    events:
      - "BOE Monetary Policy Report"
      - "BOC Monetary Policy Report" 
      - "RBA Monetary Policy Statement"
      - "RBNZ Monetary Policy Statement"
      - "Monetary Policy Summary"
      - "BOJ Outlook Report"
      - "Monetary Policy Meeting Minutes"
      - "SNB Monetary Policy Assessment"
      - "Monetary Policy Statement"
      - "BOC Rate Statement"
      - "RBA Rate Statement"
      - "RBNZ Rate Statement"
      - "MPC Official Bank Rate Votes"
    
    processing_method: "nlp_required"
    sentiment_approach: "document_analysis"
    confidence: 0.4

  # Special Events
  special_events:
    events:
      - "Annual Budget Release"
      - "Treasury Currency Report"
      - "European Parliamentary Elections"
      - "French Parliamentary Elections"
      - "Euro Summit"
      - "30-y Bond Auction"
      - "10-y Bond Auction"
    
    processing_method: "manual_review"
    sentiment_approach: "case_by_case"
    confidence: 0.2

# =============================================================================
# ASSET IMPACT MAPPING
# =============================================================================

asset_mappings:
  # Gold (XAU/USD) - Primary focus
  XAUUSD:
    primary_currencies: ["USD"]
    correlation: "inverse"  # USD strength = Gold weakness
    
  # Major USD Pairs
  EURUSD:
    primary_currencies: ["EUR", "USD"]
    correlation: "direct"   # EUR strength = EUR/USD up
    
  GBPUSD:
    primary_currencies: ["GBP", "USD"] 
    correlation: "direct"
    
  USDJPY:
    primary_currencies: ["USD", "JPY"]
    correlation: "direct"   # USD strength = USD/JPY up

# =============================================================================
# CONFIDENCE SCORING
# =============================================================================

confidence_factors:
  data_quality:
    both_actual_forecast: 1.0
    actual_only: 0.8
    forecast_only: 0.3
    neither: 0.1
    
  event_importance:
    tier_1: 1.0  # NFP, CPI, Fed Rate, FOMC Statement
    tier_2: 0.8  # PMI, GDP, Retail Sales
    tier_3: 0.6  # Regional data, consumer confidence
    
  surprise_magnitude:
    large_surprise: 1.0    # > 2 standard deviations
    medium_surprise: 0.8   # 1-2 standard deviations  
    small_surprise: 0.5    # < 1 standard deviation

# =============================================================================
# PROCESSING SETTINGS
# =============================================================================

processing_settings:
  minimum_confidence: 0.3
  default_sentiment: "neutral"
  quantitative_threshold: 0.05  # Default minimum % change to trigger sentiment
  
  # FIXED: Standardized category-specific thresholds
  category_thresholds:
    inflation: 0.05        # CPI, PPI - very sensitive
    employment: 0.05       # NFP, unemployment - very sensitive  
    growth: 0.05          # GDP, retail sales - reduced from 0.1
    business_activity: 0.05 # PMI - reduced from 0.5 for better sensitivity
    consumer: 0.05         # Consumer confidence - reduced from 2.0
    interest_rates: 0.01   # Central bank rates - most sensitive
  
  # Future NLP settings (Phase 2B)
  nlp_enabled: false
  nlp_model: "finbert"  # Financial BERT for future implementation
  hybrid_weight: 0.7    # Weight for rule-based vs NLP (70% rules, 30% NLP)

# =============================================================================
# CURRENCY SENTIMENT RULES - Asset Correlation Validation
# =============================================================================

currency_sentiment_rules:
  EUR:
    direct_impact: ["EURUSD", "EURGBP", "EURJPY", "EURCHF"]
    sentiment_mapping:
      bullish_eur: 
        EURUSD: "bullish"
        EURGBP: "bullish"
        EURJPY: "bullish"
        EURCHF: "bullish"
      bearish_eur:
        EURUSD: "bearish"
        EURGBP: "bearish" 
        EURJPY: "bearish"
        EURCHF: "bearish"
  
  GBP:
    direct_impact: ["GBPUSD", "EURGBP", "GBPJPY", "GBPCHF"]
    sentiment_mapping:
      bullish_gbp:
        GBPUSD: "bullish"
        EURGBP: "bearish"  # GBP strength = EUR/GBP down
        GBPJPY: "bullish"
        GBPCHF: "bullish"
      bearish_gbp:
        GBPUSD: "bearish"
        EURGBP: "bullish"  # GBP weakness = EUR/GBP up
        GBPJPY: "bearish"
        GBPCHF: "bearish"
  
  USD:
    direct_impact: ["EURUSD", "GBPUSD", "USDJPY", "USDCHF", "XAUUSD"]
    sentiment_mapping:
      bullish_usd:
        EURUSD: "bearish"   # USD strength = EUR/USD down
        GBPUSD: "bearish"   # USD strength = GBP/USD down
        USDJPY: "bullish"   # USD strength = USD/JPY up
        USDCHF: "bullish"   # USD strength = USD/CHF up
        XAUUSD: "bearish"   # USD strength = Gold down
      bearish_usd:
        EURUSD: "bullish"   # USD weakness = EUR/USD up
        GBPUSD: "bullish"   # USD weakness = GBP/USD up
        USDJPY: "bearish"   # USD weakness = USD/JPY down
        USDCHF: "bearish"   # USD weakness = USD/CHF down
        XAUUSD: "bullish"   # USD weakness = Gold up 