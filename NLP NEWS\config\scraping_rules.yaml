# Sentiment Classification Rules for Economic Events
# Format: event_type -> {higher_than_forecast: sentiment, lower_than_forecast: sentiment}

# US Economic Indicators
us_indicators:
  "Non-Farm Payrolls":
    higher_than_forecast:
      USD: "bullish"
      XAU/USD: "bearish"  # Gold typically falls when USD strengthens
      EUR/USD: "bearish"
      GBP/USD: "bearish"
    lower_than_forecast:
      USD: "bearish"
      XAU/USD: "bullish"
      EUR/USD: "bullish"
      GBP/USD: "bullish"
      
  "Consumer Price Index":
    higher_than_forecast:  # Higher inflation
      USD: "bullish"
      XAU/USD: "bearish"
      EUR/USD: "bearish"
      GBP/USD: "bearish"
    lower_than_forecast:  # Lower inflation
      USD: "bearish"
      XAU/USD: "bullish"
      EUR/USD: "bullish"
      GBP/USD: "bullish"
      
  "Federal Funds Rate":
    higher_than_forecast:  # Rate hike
      USD: "bullish"
      XAU/USD: "bearish"
      EUR/USD: "bearish"
      GBP/USD: "bearish"
    lower_than_forecast:  # Rate cut/dovish
      USD: "bearish"
      XAU/USD: "bullish"
      EUR/USD: "bullish"
      GBP/USD: "bullish"
      
  "GDP":
    higher_than_forecast:
      USD: "bullish"
      XAU/USD: "bearish"
      EUR/USD: "bearish"
      GBP/USD: "bearish"
    lower_than_forecast:
      USD: "bearish"
      XAU/USD: "bullish"
      EUR/USD: "bullish"
      GBP/USD: "bullish"

# European Economic Indicators
eu_indicators:
  "European Central Bank":
    higher_than_forecast:  # Hawkish/Rate hike
      EUR: "bullish"
      EUR/USD: "bullish"
      XAU/USD: "neutral"  # Mixed impact
    lower_than_forecast:  # Dovish/Rate cut
      EUR: "bearish"
      EUR/USD: "bearish"
      XAU/USD: "neutral"
      
  "German GDP":
    higher_than_forecast:
      EUR: "bullish"
      EUR/USD: "bullish"
    lower_than_forecast:
      EUR: "bearish"
      EUR/USD: "bearish"

# UK Economic Indicators
uk_indicators:
  "Bank of England":
    higher_than_forecast:  # Rate hike
      GBP: "bullish"
      GBP/USD: "bullish"
      XAU/USD: "neutral"
    lower_than_forecast:  # Rate cut
      GBP: "bearish"
      GBP/USD: "bearish"
      XAU/USD: "neutral"
      
  "UK GDP":
    higher_than_forecast:
      GBP: "bullish"
      GBP/USD: "bullish"
    lower_than_forecast:
      GBP: "bearish"
      GBP/USD: "bearish"

# Default rules for unspecified events
default_rules:
  # If actual > forecast for economic growth indicators
  growth_positive:
    base_currency: "bullish"
    counter_currencies: "bearish"
    gold: "bearish"
    
  # If actual < forecast for economic growth indicators  
  growth_negative:
    base_currency: "bearish"
    counter_currencies: "bullish"
    gold: "bullish"

# Event keywords for automatic classification
event_keywords:
  inflation_indicators: ["CPI", "PPI", "PCE", "Inflation"]
  employment_indicators: ["NFP", "Employment", "Unemployment", "Jobs"]
  growth_indicators: ["GDP", "Manufacturing", "Services", "PMI"]
  monetary_policy: ["Fed", "ECB", "BOE", "Rate", "FOMC"]
  
# Confidence scoring based on data quality
confidence_rules:
  high_confidence: 0.9  # All data present, clear direction
  medium_confidence: 0.7  # Some data missing but trend clear
  low_confidence: 0.5  # Significant data missing or unclear
  no_confidence: 0.0  # Unable to determine sentiment 