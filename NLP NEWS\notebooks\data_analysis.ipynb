{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (4037063766.py, line 4)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[1], line 4\u001b[1;36m\u001b[0m\n\u001b[1;33m    This notebook consolidates and validates the 6 months of high-impact economic news data collected from ForexFactory (January - June 2024).\u001b[0m\n\u001b[1;37m         ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m invalid syntax\n"]}], "source": ["# 📊 Economic News Data Analysis & Consolidation\n", "\n", "## Overview\n", "This notebook consolidates and validates the 6 months of high-impact economic news data collected from ForexFactory (January - June 2024).\n", "\n", "## Objectives\n", "1. **Load and validate** all 26 CSV files from raw data folder\n", "2. **Data quality analysis** - check completeness, duplicates, missing values\n", "3. **Consolidate** into single master dataset\n", "4. **Prepare data** for sentiment labeling (Phase 2)\n", "5. **Export** clean consolidated dataset\n", "\n", "##\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📦 Libraries imported successfully!\n", "🐼 Pandas version: 2.2.3\n", "🔢 NumPy version: 2.1.3\n", "📊 Plotting libraries available\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import glob\n", "from datetime import datetime, timezone\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "try:\n", "    import matplotlib.pyplot as plt\n", "    import seaborn as sns\n", "    plt.style.use('default')\n", "    sns.set_palette(\"husl\")\n", "    plotting_available = True\n", "except ImportError:\n", "    plotting_available = False\n", "\n", "print(\"📦 Libraries imported successfully!\")\n", "print(f\"🐼 Pandas version: {pd.__version__}\")\n", "print(f\"🔢 NumPy version: {np.__version__}\")\n", "if plotting_available:\n", "    print(\"📊 Plotting libraries available\")\n", "else:\n", "    print(\"⚠️  Plotting libraries not available\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 📁 Step 1: Data Discovery\n", "\n", "Let's explore what CSV files we have in the raw data folder and get basic information about each file.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Found 26 CSV files in raw data folder\n", "\n", "📋 File listing:\n", " 1. forexfactory_apr1.2024_20250706_144415.csv    (1,271 bytes)\n", " 2. forexfactory_apr15.2024_20250706_144456.csv   (1,566 bytes)\n", " 3. forexfactory_apr22.2024_20250706_144514.csv   (1,913 bytes)\n", " 4. forexfactory_apr29.2024_20250706_144535.csv   (1,977 bytes)\n", " 5. forexfactory_apr8.2024_20250706_144436.csv    (1,703 bytes)\n", " 6. forexfactory_feb12.2024_20250706_144202.csv   (1,923 bytes)\n", " 7. forexfactory_feb19.2024_20250706_144222.csv   (1,459 bytes)\n", " 8. forexfactory_feb26.2024_20250706_144240.csv   (1,258 bytes)\n", " 9. forexfactory_feb5.2024_20250706_144143.csv    (1,112 bytes)\n", "10. forexfactory_jan1.2024_20250706_143931.csv    (1,188 bytes)\n", "11. forexfactory_jan15.2024_20250706_144015.csv   (1,344 bytes)\n", "12. forexfactory_jan22.2024_20250706_144104.csv   (2,049 bytes)\n", "13. forexfactory_jan29.2024_20250706_144124.csv   (2,252 bytes)\n", "14. forexfactory_jan8.2024_20250706_143957.csv    (897 bytes)\n", "15. forexfactory_jun10.2024_20250706_144733.csv   (1,797 bytes)\n", "16. forexfactory_jun17.2024_20250706_144752.csv   (2,273 bytes)\n", "17. forexfactory_jun24.2024_20250706_144812.csv   (1,297 bytes)\n", "18. forexfactory_jun3.2024_20250706_144714.csv    (1,876 bytes)\n", "19. forexfactory_mar11.2024_20250706_144319.csv   (1,330 bytes)\n", "20. forexfactory_mar18.2024_20250706_144338.csv   (3,171 bytes)\n", "21. forexfactory_mar25.2024_20250706_144357.csv   (971 bytes)\n", "22. forexfactory_mar4.2024_20250706_144300.csv    (1,929 bytes)\n", "23. forexfactory_may13.2024_20250706_144615.csv   (1,414 bytes)\n", "24. forexfactory_may20.2024_20250706_144635.csv   (1,894 bytes)\n", "25. forexfactory_may27.2024_20250706_144654.csv   (1,067 bytes)\n", "26. forexfactory_may6.2024_20250706_144554.csv    (1,408 bytes)\n", "\n", "✅ Total files found: 26\n"]}], "source": ["# Define data paths\n", "RAW_DATA_PATH = Path(\"../data/news/raw\")\n", "PROCESSED_DATA_PATH = Path(\"../data/news/processed\")\n", "PROCESSED_DATA_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "# Find all CSV files\n", "csv_files = list(RAW_DATA_PATH.glob(\"*.csv\"))\n", "csv_files.sort()  # Sort alphabetically\n", "\n", "print(f\"📊 Found {len(csv_files)} CSV files in raw data folder\")\n", "print(\"\\n📋 File listing:\")\n", "for i, file in enumerate(csv_files, 1):\n", "    file_size = file.stat().st_size\n", "    print(f\"{i:2d}. {file.name:<45} ({file_size:,} bytes)\")\n", "\n", "if len(csv_files) == 0:\n", "    print(\"❌ No CSV files found! Please check the data path.\")\n", "else:\n", "    print(f\"\\n✅ Total files found: {len(csv_files)}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 📖 Step 2: Load and Examine Individual Files\n", "\n", "Let's load a few sample files to understand the data structure and check for consistency.\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Examining sample files...\n", "\n", "📄 File: forexfactory_apr1.2024_20250706_144415.csv\n", "   Rows: 13\n", "   Columns: ['timestamp', 'currency', 'event', 'impact', 'actual', 'forecast', 'previous', 'scraped_at']\n", "   Missing values: {'timestamp': 0, 'currency': 0, 'event': 0, 'impact': 0, 'actual': 1, 'forecast': 1, 'previous': 1, 'scraped_at': 0}\n", "   Sample data:\n", "     Row 1: {'timestamp': '2024-04-01T19:45:00', 'currency': 'USD', 'event': 'ISM Manufacturing PMI', 'impact': 'High', 'actual': '50.3', 'forecast': '48.5', 'previous': '47.8', 'scraped_at': '2025-07-06T08:59:13.732158'}\n", "     Row 2: {'timestamp': '2024-04-02T12:14:00', 'currency': 'EUR', 'event': 'German Prelim CPI m/m', 'impact': 'High', 'actual': '0.4%', 'forecast': '0.5%', 'previous': '0.4%', 'scraped_at': '2025-07-06T08:59:13.732169'}\n", "\n", "📄 File: forexfactory_apr15.2024_20250706_144456.csv\n", "   Rows: 17\n", "   Columns: ['timestamp', 'currency', 'event', 'impact', 'actual', 'forecast', 'previous', 'scraped_at']\n", "   Missing values: {'timestamp': 0, 'currency': 0, 'event': 0, 'impact': 0, 'actual': 4, 'forecast': 4, 'previous': 4, 'scraped_at': 0}\n", "   Sample data:\n", "     Row 1: {'timestamp': '2024-04-15T18:15:00', 'currency': 'USD', 'event': 'Core Retail Sales m/m', 'impact': 'High', 'actual': '1.1%', 'forecast': '0.5%', 'previous': '0.3%', 'scraped_at': '2025-07-06T08:59:54.109901'}\n", "     Row 2: {'timestamp': '2024-04-15T18:15:00', 'currency': 'USD', 'event': 'Empire State Manufacturing Index', 'impact': 'High', 'actual': '-14.3', 'forecast': '-5.2', 'previous': '-20.9', 'scraped_at': '2025-07-06T08:59:54.109910'}\n", "\n", "📄 File: forexfactory_apr22.2024_20250706_144514.csv\n", "   Rows: 20\n", "   Columns: ['timestamp', 'currency', 'event', 'impact', 'actual', 'forecast', 'previous', 'scraped_at']\n", "   Missing values: {'timestamp': 0, 'currency': 0, 'event': 0, 'impact': 0, 'actual': 3, 'forecast': 3, 'previous': 3, 'scraped_at': 0}\n", "   Sample data:\n", "     Row 1: {'timestamp': '2024-04-23T13:00:00', 'currency': 'EUR', 'event': 'French Flash Manufacturing PMI', 'impact': 'High', 'actual': '44.9', 'forecast': '46.9', 'previous': '45.8', 'scraped_at': '2025-07-06T09:00:12.870749'}\n", "     Row 2: {'timestamp': '2024-04-23T13:00:00', 'currency': 'EUR', 'event': 'French Flash Services PMI', 'impact': 'High', 'actual': '50.5', 'forecast': '48.9', 'previous': '47.8', 'scraped_at': '2025-07-06T09:00:12.870759'}\n", "\n", "✅ Sample examination complete!\n"]}], "source": ["# Function to load and examine a single CSV file\n", "def examine_csv_file(file_path):\n", "    \"\"\"Load and examine a single CSV file\"\"\"\n", "    try:\n", "        df = pd.read_csv(file_path)\n", "        return {\n", "            'filename': file_path.name,\n", "            'rows': len(df),\n", "            'columns': list(df.columns),\n", "            'dtypes': df.dtypes.to_dict(),\n", "            'sample_data': df.head(2).to_dict('records'),\n", "            'missing_values': df.isnull().sum().to_dict(),\n", "            'success': True\n", "        }\n", "    except Exception as e:\n", "        return {\n", "            'filename': file_path.name,\n", "            'error': str(e),\n", "            'success': <PERSON><PERSON><PERSON>\n", "        }\n", "\n", "# Examine first 3 files as samples\n", "print(\"🔍 Examining sample files...\")\n", "sample_files = csv_files[:3] if len(csv_files) >= 3 else csv_files\n", "\n", "for file_path in sample_files:\n", "    result = examine_csv_file(file_path)\n", "    print(f\"\\n📄 File: {result['filename']}\")\n", "    \n", "    if result['success']:\n", "        print(f\"   Rows: {result['rows']}\")\n", "        print(f\"   Columns: {result['columns']}\")\n", "        print(f\"   Missing values: {result['missing_values']}\")\n", "        print(f\"   Sample data:\")\n", "        for i, record in enumerate(result['sample_data']):\n", "            print(f\"     Row {i+1}: {record}\")\n", "    else:\n", "        print(f\"   ❌ Error: {result['error']}\")\n", "\n", "print(f\"\\n✅ Sample examination complete!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 🔄 Step 3: Load All Files and Consolidate\n", "\n", "Now let's load all CSV files and consolidate them into a single master dataset.\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📥 Loading all CSV files...\n", "✅ Loaded forexfactory_apr1.2024_20250706_144415.csv: 13 rows\n", "✅ Loaded forexfactory_apr15.2024_20250706_144456.csv: 17 rows\n", "✅ Loaded forexfactory_apr22.2024_20250706_144514.csv: 20 rows\n", "✅ Loaded forexfactory_apr29.2024_20250706_144535.csv: 21 rows\n", "✅ Loaded forexfactory_apr8.2024_20250706_144436.csv: 19 rows\n", "✅ Loaded forexfactory_feb12.2024_20250706_144202.csv: 21 rows\n", "✅ Loaded forexfactory_feb19.2024_20250706_144222.csv: 15 rows\n", "✅ Loaded forexfactory_feb26.2024_20250706_144240.csv: 13 rows\n", "✅ Loaded forexfactory_feb5.2024_20250706_144143.csv: 12 rows\n", "✅ Loaded forexfactory_jan1.2024_20250706_143931.csv: 12 rows\n", "✅ Loaded forexfactory_jan15.2024_20250706_144015.csv: 14 rows\n", "✅ Loaded forexfactory_jan22.2024_20250706_144104.csv: 22 rows\n", "✅ Loaded forexfactory_jan29.2024_20250706_144124.csv: 24 rows\n", "✅ Loaded forexfactory_jan8.2024_20250706_143957.csv: 10 rows\n", "✅ Loaded forexfactory_jun10.2024_20250706_144733.csv: 20 rows\n", "✅ Loaded forexfactory_jun17.2024_20250706_144752.csv: 24 rows\n", "✅ Loaded forexfactory_jun24.2024_20250706_144812.csv: 14 rows\n", "✅ Loaded forexfactory_jun3.2024_20250706_144714.csv: 20 rows\n", "✅ Loaded forexfactory_mar11.2024_20250706_144319.csv: 14 rows\n", "✅ Loaded forexfactory_mar18.2024_20250706_144338.csv: 35 rows\n", "✅ Loaded forexfactory_mar25.2024_20250706_144357.csv: 10 rows\n", "✅ Loaded forexfactory_mar4.2024_20250706_144300.csv: 21 rows\n", "✅ Loaded forexfactory_may13.2024_20250706_144615.csv: 15 rows\n", "✅ Loaded forexfactory_may20.2024_20250706_144635.csv: 20 rows\n", "✅ Loaded forexfactory_may27.2024_20250706_144654.csv: 11 rows\n", "✅ Loaded forexfactory_may6.2024_20250706_144554.csv: 15 rows\n", "\n", "🎉 Successfully consolidated 26 files!\n", "📊 Total rows in consolidated dataset: 452\n", "📊 Total columns: 10\n", "📊 Date range: 2024-01-03T20:45:00 to 2024-06-30T14:15:00\n"]}], "source": ["# Load all CSV files and consolidate\n", "print(\"📥 Loading all CSV files...\")\n", "\n", "all_dataframes = []\n", "load_errors = []\n", "\n", "for file_path in csv_files:\n", "    try:\n", "        df = pd.read_csv(file_path)\n", "        \n", "        # Add source file information\n", "        df['source_file'] = file_path.name\n", "        df['week_period'] = file_path.name.split('_')[1].split('.')[0]  # Extract week period\n", "        \n", "        all_dataframes.append(df)\n", "        print(f\"✅ Loaded {file_path.name}: {len(df)} rows\")\n", "        \n", "    except Exception as e:\n", "        error_msg = f\"❌ Failed to load {file_path.name}: {str(e)}\"\n", "        load_errors.append(error_msg)\n", "        print(error_msg)\n", "\n", "# Consolidate all dataframes\n", "if all_dataframes:\n", "    consolidated_df = pd.concat(all_dataframes, ignore_index=True)\n", "    print(f\"\\n🎉 Successfully consolidated {len(all_dataframes)} files!\")\n", "    print(f\"📊 Total rows in consolidated dataset: {len(consolidated_df):,}\")\n", "    print(f\"📊 Total columns: {len(consolidated_df.columns)}\")\n", "    print(f\"📊 Date range: {consolidated_df['timestamp'].min()} to {consolidated_df['timestamp'].max()}\")\n", "else:\n", "    print(\"❌ No files were successfully loaded!\")\n", "\n", "if load_errors:\n", "    print(f\"\\n⚠️  {len(load_errors)} files had errors:\")\n", "    for error in load_errors:\n", "        print(f\"   {error}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 🔍 Step 4: Data Quality Analysis\n", "\n", "Let's analyze the quality of our consolidated dataset and identify any issues.\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 DATA QUALITY ANALYSIS\n", "==================================================\n", "📊 Dataset Shape: (452, 10)\n", "📊 Memory Usage: 0.26 MB\n", "\n", "📋 Columns (10):\n", "   • timestamp\n", "   • currency\n", "   • event\n", "   • impact\n", "   • actual\n", "   • forecast\n", "   • previous\n", "   • scraped_at\n", "   • source_file\n", "   • week_period\n", "\n", "🏷️  Data Types:\n", "   • timestamp      : object\n", "   • currency       : object\n", "   • event          : object\n", "   • impact         : object\n", "   • actual         : object\n", "   • forecast       : object\n", "   • previous       : object\n", "   • scraped_at     : object\n", "   • source_file    : object\n", "   • week_period    : object\n", "\n", "❓ Missing Values:\n", "   • timestamp      : 0 (0.0%)\n", "   • currency       : 0 (0.0%)\n", "   • event          : 0 (0.0%)\n", "   • impact         : 0 (0.0%)\n", "   • actual         : 102 (22.57%)\n", "   • forecast       : 108 (23.89%)\n", "   • previous       : 102 (22.57%)\n", "   • scraped_at     : 0 (0.0%)\n", "   • source_file    : 0 (0.0%)\n", "   • week_period    : 0 (0.0%)\n", "\n", "🔢 Unique Values:\n", "   • currency       : 8 unique values\n", "     Top values: {'USD': np.int64(199), 'GBP': np.int64(61), 'CAD': np.int64(59), 'EUR': np.int64(47), 'AUD': np.int64(41), 'NZD': np.int64(18), 'JPY': np.int64(15), 'CHF': np.int64(12)}\n", "   • impact         : 1 unique values\n", "     Top values: {'High': np.int64(452)}\n", "   • event          : 92 unique values\n"]}], "source": ["# Data Quality Analysis\n", "print(\"🔍 DATA QUALITY ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "if 'consolidated_df' in locals():\n", "    df = consolidated_df.copy()\n", "    \n", "    # Basic info\n", "    print(f\"📊 Dataset Shape: {df.shape}\")\n", "    print(f\"📊 Memory Usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "    \n", "    # Column information\n", "    print(f\"\\n📋 Columns ({len(df.columns)}):\")\n", "    for col in df.columns:\n", "        print(f\"   • {col}\")\n", "    \n", "    # Data types\n", "    print(f\"\\n🏷️  Data Types:\")\n", "    for col, dtype in df.dtypes.items():\n", "        print(f\"   • {col:<15}: {dtype}\")\n", "    \n", "    # Missing values analysis\n", "    print(f\"\\n❓ Missing Values:\")\n", "    missing = df.isnull().sum()\n", "    missing_pct = (missing / len(df) * 100).round(2)\n", "    \n", "    for col in df.columns:\n", "        if missing[col] > 0:\n", "            print(f\"   • {col:<15}: {missing[col]:,} ({missing_pct[col]}%)\")\n", "        else:\n", "            print(f\"   • {col:<15}: 0 (0.0%)\")\n", "    \n", "    # Unique values in key columns\n", "    print(f\"\\n🔢 Unique Values:\")\n", "    key_columns = ['currency', 'impact', 'event']\n", "    for col in key_columns:\n", "        if col in df.columns:\n", "            unique_count = df[col].nunique()\n", "            print(f\"   • {col:<15}: {unique_count:,} unique values\")\n", "            if unique_count <= 20:  # Show values if not too many\n", "                values = df[col].value_counts().head(10)\n", "                print(f\"     Top values: {dict(values)}\")\n", "    \n", "else:\n", "    print(\"❌ No consolidated dataset available!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 🧹 Step 5: Data Cleaning and Validation\n", "\n", "Let's clean the data and prepare it for sentiment analysis.\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧹 DATA CLEANING AND VALIDATION\n", "==================================================\n", "📊 Starting with 452 rows\n", "\n", "🕐 Converting timestamps...\n", "✅ Timestamp conversion successful\n", "\n", "🔄 Removing duplicates...\n", "✅ Removed 0 duplicate rows\n", "\n", "🎯 Filtering for high-impact events...\n", "✅ Kept 452 high-impact events (was 452)\n", "\n", "📅 Sorting by timestamp...\n", "✅ Data sorted chronologically\n", "\n", "➕ Adding derived columns...\n", "✅ Added time-based columns\n", "\n", "📊 FINAL CLEAN DATASET:\n", "   • Total rows: 452\n", "   • Date range: 2024-01-03 to 2024-06-30\n", "   • Currencies: ['AUD', 'CAD', 'CHF', 'EUR', 'GBP', 'JPY', 'NZD', 'USD']\n", "   • Events per month: {1: 67, 2: 74, 3: 82, 4: 73, 5: 78, 6: 78}\n"]}], "source": ["# Data Cleaning and Validation\n", "print(\"🧹 DATA CLEANING AND VALIDATION\")\n", "print(\"=\" * 50)\n", "\n", "if 'consolidated_df' in locals():\n", "    df_clean = consolidated_df.copy()\n", "    \n", "    print(f\"📊 Starting with {len(df_clean):,} rows\")\n", "    \n", "    # 1. Convert timestamp to datetime\n", "    print(\"\\n🕐 Converting timestamps...\")\n", "    try:\n", "        df_clean['timestamp'] = pd.to_datetime(df_clean['timestamp'])\n", "        df_clean['scraped_at'] = pd.to_datetime(df_clean['scraped_at'])\n", "        print(\"✅ Timestamp conversion successful\")\n", "    except Exception as e:\n", "        print(f\"❌ Timestamp conversion failed: {e}\")\n", "    \n", "    # 2. <PERSON><PERSON><PERSON> duplicates\n", "    print(\"\\n🔄 Removing duplicates...\")\n", "    initial_count = len(df_clean)\n", "    \n", "    # Define key columns for duplicate detection\n", "    key_cols = ['timestamp', 'currency', 'event', 'actual', 'forecast', 'previous']\n", "    available_key_cols = [col for col in key_cols if col in df_clean.columns]\n", "    \n", "    df_clean = df_clean.drop_duplicates(subset=available_key_cols, keep='first')\n", "    duplicates_removed = initial_count - len(df_clean)\n", "    print(f\"✅ Removed {duplicates_removed:,} duplicate rows\")\n", "    \n", "    # 3. <PERSON>lter for high-impact events only\n", "    print(\"\\n🎯 Filtering for high-impact events...\")\n", "    if 'impact' in df_clean.columns:\n", "        high_impact_count = len(df_clean[df_clean['impact'] == 'High'])\n", "        df_clean = df_clean[df_clean['impact'] == 'High'].copy()\n", "        print(f\"✅ Kept {len(df_clean):,} high-impact events (was {high_impact_count:,})\")\n", "    else:\n", "        print(\"⚠️  No 'impact' column found - keeping all events\")\n", "    \n", "    # 4. Sort by timestamp\n", "    print(\"\\n📅 Sorting by timestamp...\")\n", "    df_clean = df_clean.sort_values('timestamp').reset_index(drop=True)\n", "    print(f\"✅ Data sorted chronologically\")\n", "    \n", "    # 5. Add derived columns\n", "    print(\"\\n➕ Adding derived columns...\")\n", "    df_clean['year'] = df_clean['timestamp'].dt.year\n", "    df_clean['month'] = df_clean['timestamp'].dt.month\n", "    df_clean['day_of_week'] = df_clean['timestamp'].dt.day_name()\n", "    df_clean['hour'] = df_clean['timestamp'].dt.hour\n", "    print(\"✅ Added time-based columns\")\n", "    \n", "    # 6. Final summary\n", "    print(f\"\\n📊 FINAL CLEAN DATASET:\")\n", "    print(f\"   • Total rows: {len(df_clean):,}\")\n", "    print(f\"   • Date range: {df_clean['timestamp'].min().strftime('%Y-%m-%d')} to {df_clean['timestamp'].max().strftime('%Y-%m-%d')}\")\n", "    print(f\"   • Currencies: {sorted(df_clean['currency'].unique()) if 'currency' in df_clean.columns else 'N/A'}\")\n", "    print(f\"   • Events per month: {df_clean.groupby('month').size().to_dict()}\")\n", "    \n", "else:\n", "    print(\"❌ No consolidated dataset available!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 💾 Step 6: Export Clean Dataset\n", "\n", "Save the cleaned and consolidated dataset for Phase 2 (sentiment labeling).\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💾 EXPORTING CLEAN DATASET\n", "==================================================\n", "📁 Saving consolidated dataset to: ..\\data\\news\\processed\\consolidated_economic_events.csv\n", "✅ Dataset saved successfully (69.7 KB)\n", "\n", "📄 Creating summary report: ..\\data\\news\\processed\\data_summary.txt\n", "❌ Failed to create summary: 'charmap' codec can't encode character '\\U0001f4ca' in position 0: character maps to <undefined>\n", "\n", "🎉 DATA CONSOLIDATION COMPLETE!\n", "📊 Final dataset: 452 high-impact economic events\n", "📁 Files saved in: ..\\data\\news\\processed\n", "🚀 Ready for Phase 2: Sentiment Labeling!\n"]}], "source": ["# Export Clean Dataset\n", "print(\"💾 EXPORTING CLEAN DATASET\")\n", "print(\"=\" * 50)\n", "\n", "if 'df_clean' in locals():\n", "    # Define output paths\n", "    output_file = PROCESSED_DATA_PATH / \"consolidated_economic_events.csv\"\n", "    summary_file = PROCESSED_DATA_PATH / \"data_summary.txt\"\n", "    \n", "    # Export main dataset\n", "    print(f\"📁 Saving consolidated dataset to: {output_file}\")\n", "    try:\n", "        df_clean.to_csv(output_file, index=False)\n", "        file_size = output_file.stat().st_size / 1024  # KB\n", "        print(f\"✅ Dataset saved successfully ({file_size:.1f} KB)\")\n", "    except Exception as e:\n", "        print(f\"❌ Failed to save dataset: {e}\")\n", "    \n", "    # Create summary report\n", "    print(f\"\\n📄 Creating summary report: {summary_file}\")\n", "    try:\n", "        with open(summary_file, 'w') as f:\n", "            f.write(\"ECONOMIC NEWS DATA CONSOLIDATION SUMMARY\\n\")\n", "            f.write(\"=\" * 50 + \"\\n\\n\")\n", "            f.write(f\"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n\")\n", "            \n", "            f.write(f\"📊 DATASET OVERVIEW:\\n\")\n", "            f.write(f\"   • Total Events: {len(df_clean):,}\\n\")\n", "            f.write(f\"   • Date Range: {df_clean['timestamp'].min().strftime('%Y-%m-%d')} to {df_clean['timestamp'].max().strftime('%Y-%m-%d')}\\n\")\n", "            f.write(f\"   • Source Files: {len(csv_files)}\\n\")\n", "            f.write(f\"   • Columns: {len(df_clean.columns)}\\n\\n\")\n", "            \n", "            f.write(f\"📋 COLUMNS:\\n\")\n", "            for col in df_clean.columns:\n", "                f.write(f\"   • {col}\\n\")\n", "            f.write(\"\\n\")\n", "            \n", "            if 'currency' in df_clean.columns:\n", "                f.write(f\"💱 CURRENCIES:\\n\")\n", "                currency_counts = df_clean['currency'].value_counts()\n", "                for curr, count in currency_counts.items():\n", "                    f.write(f\"   • {curr}: {count:,} events\\n\")\n", "                f.write(\"\\n\")\n", "            \n", "            f.write(f\"📅 MONTHLY DISTRIBUTION:\\n\")\n", "            monthly_counts = df_clean.groupby('month').size()\n", "            month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']\n", "            for month, count in monthly_counts.items():\n", "                f.write(f\"   • {month_names[month-1]}: {count:,} events\\n\")\n", "            \n", "        print(\"✅ Summary report created successfully\")\n", "    except Exception as e:\n", "        print(f\"❌ Failed to create summary: {e}\")\n", "    \n", "    # Display final success message\n", "    print(f\"\\n🎉 DATA CONSOLIDATION COMPLETE!\")\n", "    print(f\"📊 Final dataset: {len(df_clean):,} high-impact economic events\")\n", "    print(f\"📁 Files saved in: {PROCESSED_DATA_PATH}\")\n", "    print(f\"🚀 Ready for Phase 2: Sentiment Labeling!\")\n", "    \n", "else:\n", "    print(\"❌ No clean dataset available to export!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📊 ForexFactory Data Analysis & Consolidation\n", "\n", "This notebook analyzes and consolidates scraped ForexFactory economic calendar data with comprehensive validation.\n", "\n", "## Goals:\n", "1. **Data Quality Analysis** - Check completeness, duplicates, missing values\n", "2. **Timestamp Validation** - Ensure proper UTC timestamps and chronological order  \n", "3. **Coverage Analysis** - Identify missing weeks/gaps in data\n", "4. **Smart Consolidation** - Merge all CSV files with deduplication\n", "5. **Data Statistics** - Generate summary statistics and insights\n", "\n", "## Dataset Overview:\n", "- **Source**: ForexFactory Economic Calendar\n", "- **Period**: January - June 2024 (6 months)\n", "- **Focus**: High-impact economic events only\n", "- **Fields**: timestamp, currency, event, impact, actual, forecast, previous, scraped_at\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 ForexFactory Data Analysis Notebook\n", "==================================================\n", "Raw data directory: ..\\data\\news\\raw\n", "Processed data directory: ..\\data\\news\\processed\n", "Cache directory: ..\\data\\news\\cache\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "from datetime import datetime, timedelta\n", "import re\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configuration\n", "RAW_DATA_DIR = Path('../data/news/raw')\n", "PROCESSED_DATA_DIR = Path('../data/news/processed')\n", "CACHE_DIR = Path('../data/news/cache')\n", "\n", "print(\"📊 ForexFactory Data Analysis Notebook\")\n", "print(\"=\" * 50)\n", "print(f\"Raw data directory: {RAW_DATA_DIR}\")\n", "print(f\"Processed data directory: {PROCESSED_DATA_DIR}\")\n", "print(f\"Cache directory: {CACHE_DIR}\")\n", "\n", "# Create directories if they don't exist\n", "PROCESSED_DATA_DIR.mkdir(parents=True, exist_ok=True)\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 STEP 1: Data Discovery\n", "------------------------------\n", "Found 26 CSV files:\n", "   1. forexfactory_apr1.2024_20250706_144415.csv (1.2 KB, 2025-07-06 14:44)\n", "   2. forexfactory_apr15.2024_20250706_144456.csv (1.5 KB, 2025-07-06 14:44)\n", "   3. forexfactory_apr22.2024_20250706_144514.csv (1.9 KB, 2025-07-06 14:45)\n", "   4. forexfactory_apr29.2024_20250706_144535.csv (1.9 KB, 2025-07-06 14:45)\n", "   5. forexfactory_apr8.2024_20250706_144436.csv (1.7 KB, 2025-07-06 14:44)\n", "   6. forexfactory_feb12.2024_20250706_144202.csv (1.9 KB, 2025-07-06 14:42)\n", "   7. forexfactory_feb19.2024_20250706_144222.csv (1.4 KB, 2025-07-06 14:42)\n", "   8. forexfactory_feb26.2024_20250706_144240.csv (1.2 KB, 2025-07-06 14:42)\n", "   9. forexfactory_feb5.2024_20250706_144143.csv (1.1 KB, 2025-07-06 14:41)\n", "  10. forexfactory_jan1.2024_20250706_143931.csv (1.2 KB, 2025-07-06 14:39)\n", "  11. forexfactory_jan15.2024_20250706_144015.csv (1.3 KB, 2025-07-06 14:40)\n", "  12. forexfactory_jan22.2024_20250706_144104.csv (2.0 KB, 2025-07-06 14:41)\n", "  13. forexfactory_jan29.2024_20250706_144124.csv (2.2 KB, 2025-07-06 14:41)\n", "  14. forexfactory_jan8.2024_20250706_143957.csv (0.9 KB, 2025-07-06 14:39)\n", "  15. forexfactory_jun10.2024_20250706_144733.csv (1.8 KB, 2025-07-06 14:47)\n", "  16. forexfactory_jun17.2024_20250706_144752.csv (2.2 KB, 2025-07-06 14:47)\n", "  17. forexfactory_jun24.2024_20250706_144812.csv (1.3 KB, 2025-07-06 14:48)\n", "  18. forexfactory_jun3.2024_20250706_144714.csv (1.8 KB, 2025-07-06 14:47)\n", "  19. forexfactory_mar11.2024_20250706_144319.csv (1.3 KB, 2025-07-06 14:43)\n", "  20. forexfactory_mar18.2024_20250706_144338.csv (3.1 KB, 2025-07-06 14:43)\n", "  21. forexfactory_mar25.2024_20250706_144357.csv (0.9 KB, 2025-07-06 14:43)\n", "  22. forexfactory_mar4.2024_20250706_144300.csv (1.9 KB, 2025-07-06 14:43)\n", "  23. forexfactory_may13.2024_20250706_144615.csv (1.4 KB, 2025-07-06 14:46)\n", "  24. forexfactory_may20.2024_20250706_144635.csv (1.8 KB, 2025-07-06 14:46)\n", "  25. forexfactory_may27.2024_20250706_144654.csv (1.0 KB, 2025-07-06 14:46)\n", "  26. forexfactory_may6.2024_20250706_144554.csv (1.4 KB, 2025-07-06 14:45)\n", "\n", "✅ Ready to analyze 26 files\n"]}], "source": ["# 1. DISCOVER AND LOAD DATA\n", "print(\"\\n🔍 STEP 1: Data Discovery\")\n", "print(\"-\" * 30)\n", "\n", "# Find all CSV files in raw directory\n", "csv_files = list(RAW_DATA_DIR.glob('forexfactory_*.csv'))\n", "csv_files.sort()\n", "\n", "print(f\"Found {len(csv_files)} CSV files:\")\n", "for i, file in enumerate(csv_files, 1):\n", "    file_size = file.stat().st_size / 1024  # KB\n", "    mod_time = datetime.fromtimestamp(file.stat().st_mtime)\n", "    print(f\"  {i:2d}. {file.name} ({file_size:.1f} KB, {mod_time.strftime('%Y-%m-%d %H:%M')})\")\n", "\n", "if not csv_files:\n", "    print(\"❌ No CSV files found! Make sure batch scraper has completed.\")\n", "else:\n", "    print(f\"\\n✅ Ready to analyze {len(csv_files)} files\")\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📂 STEP 2: Individual File Analysis\n", "----------------------------------------\n", "✅ apr1.2024: 13 events, 12/13 with actuals\n", "✅ apr15.2024: 17 events, 13/17 with actuals\n", "✅ apr22.2024: 20 events, 17/20 with actuals\n", "✅ apr29.2024: 21 events, 17/21 with actuals\n", "✅ apr8.2024: 19 events, 12/19 with actuals\n", "✅ feb12.2024: 21 events, 18/21 with actuals\n", "✅ feb19.2024: 15 events, 13/15 with actuals\n", "✅ feb26.2024: 13 events, 11/13 with actuals\n", "✅ feb5.2024: 12 events, 7/12 with actuals\n", "✅ jan1.2024: 12 events, 11/12 with actuals\n", "✅ jan15.2024: 14 events, 13/14 with actuals\n", "✅ jan22.2024: 22 events, 14/22 with actuals\n", "✅ jan29.2024: 24 events, 18/24 with actuals\n", "✅ jan8.2024: 10 events, 9/10 with actuals\n", "✅ jun10.2024: 20 events, 13/20 with actuals\n", "✅ jun17.2024: 24 events, 19/24 with actuals\n", "✅ jun24.2024: 14 events, 11/14 with actuals\n", "✅ jun3.2024: 20 events, 15/20 with actuals\n", "✅ mar11.2024: 14 events, 14/14 with actuals\n", "✅ mar18.2024: 35 events, 23/35 with actuals\n", "✅ mar25.2024: 10 events, 8/10 with actuals\n", "✅ mar4.2024: 21 events, 13/21 with actuals\n", "✅ may13.2024: 15 events, 14/15 with actuals\n", "✅ may20.2024: 20 events, 16/20 with actuals\n", "✅ may27.2024: 11 events, 10/11 with actuals\n", "✅ may6.2024: 15 events, 9/15 with actuals\n", "\n", "📊 Successfully loaded 26 files with 452 total events\n"]}], "source": ["# 2. LOAD AND <PERSON><PERSON><PERSON><PERSON><PERSON> INDIVIDUAL FILES\n", "print(\"\\n📂 STEP 2: Individual File Analysis\")\n", "print(\"-\" * 40)\n", "\n", "file_stats = []\n", "all_dataframes = []\n", "\n", "for file in csv_files:\n", "    try:\n", "        # Load CSV\n", "        df = pd.read_csv(file)\n", "        \n", "        # Extract week from filename\n", "        week_match = re.search(r'forexfactory_(.+?)_\\d{8}', file.name)\n", "        week = week_match.group(1) if week_match else file.name\n", "        \n", "        # Basic stats\n", "        stats = {\n", "            'file': file.name,\n", "            'week': week,\n", "            'events': len(df),\n", "            'currencies': df['currency'].nunique(),\n", "            'date_range': f\"{df['timestamp'].min()} to {df['timestamp'].max()}\",\n", "            'has_actuals': (df['actual'].notna() & (df['actual'] != '')).sum(),\n", "            'has_forecasts': (df['forecast'].notna() & (df['forecast'] != '')).sum(),\n", "            'missing_data': df.isnull().sum().sum()\n", "        }\n", "        \n", "        file_stats.append(stats)\n", "        all_dataframes.append(df.assign(source_file=file.name, week=week))\n", "        \n", "        print(f\"✅ {week}: {len(df)} events, {stats['has_actuals']}/{len(df)} with actuals\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error loading {file.name}: {e}\")\n", "\n", "print(f\"\\n📊 Successfully loaded {len(all_dataframes)} files with {sum(len(df) for df in all_dataframes)} total events\")\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔗 STEP 3: Data Consolidation\n", "-----------------------------------\n", "Combined dataset: 452 total events\n", "Found 0 duplicate events\n", "✅ Clean dataset: 452 unique events\n", "📅 Date range: 2024-01-03 20:45:00 to 2024-06-30 14:15:00\n"]}], "source": ["# 3. CONSOLIDATE DATA WITH VALIDATION\n", "print(\"\\n🔗 STEP 3: Data Consolidation\")\n", "print(\"-\" * 35)\n", "\n", "if all_dataframes:\n", "    # Combine all dataframes\n", "    combined_df = pd.concat(all_dataframes, ignore_index=True)\n", "    print(f\"Combined dataset: {len(combined_df)} total events\")\n", "    \n", "    # Convert timestamp to datetime\n", "    combined_df['timestamp'] = pd.to_datetime(combined_df['timestamp'])\n", "    combined_df['scraped_at'] = pd.to_datetime(combined_df['scraped_at'])\n", "    \n", "    # Identify duplicates (same timestamp, currency, event)\n", "    duplicate_mask = combined_df.duplicated(subset=['timestamp', 'currency', 'event'], keep='first')\n", "    duplicates = combined_df[duplicate_mask]\n", "    \n", "    print(f\"Found {len(duplicates)} duplicate events\")\n", "    \n", "    if len(duplicates) > 0:\n", "        print(\"Sample duplicates:\")\n", "        print(duplicates[['timestamp', 'currency', 'event', 'week']].head())\n", "    \n", "    # Remove duplicates\n", "    clean_df = combined_df[~duplicate_mask].copy()\n", "    \n", "    # Sort by timestamp\n", "    clean_df = clean_df.sort_values('timestamp').reset_index(drop=True)\n", "    \n", "    print(f\"✅ Clean dataset: {len(clean_df)} unique events\")\n", "    print(f\"📅 Date range: {clean_df['timestamp'].min()} to {clean_df['timestamp'].max()}\")\n", "    \n", "else:\n", "    print(\"❌ No data to consolidate\")\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 STEP 4: Data Quality Analysis\n", "--------------------------------------\n", "Missing Data Analysis:\n", "  actual: 102 (22.6%)\n", "  forecast: 108 (23.9%)\n", "  previous: 102 (22.6%)\n", "\n", "Currency Distribution:\n", "  USD: 199 events (44.0%)\n", "  GBP: 61 events (13.5%)\n", "  CAD: 59 events (13.1%)\n", "  EUR: 47 events (10.4%)\n", "  AUD: 41 events (9.1%)\n", "  NZD: 18 events (4.0%)\n", "  JPY: 15 events (3.3%)\n", "  CHF: 12 events (2.7%)\n", "\n", "Data Completeness:\n", "  Events with actual values: 350 (77.4%)\n", "  Events with forecast values: 344 (76.1%)\n", "  Events with both actual & forecast: 344 (76.1%)\n", "\n", "Timeline Analysis:\n", "  Total days with events: 112\n", "  Average events per day: 4.0\n", "  Busiest day: 2024-03-21 (19 events)\n"]}], "source": ["# 4. DATA QUALITY ANALYSIS\n", "print(\"\\n🔍 STEP 4: Data Quality Analysis\")\n", "print(\"-\" * 38)\n", "\n", "if 'clean_df' in locals() and len(clean_df) > 0:\n", "    \n", "    # Missing data analysis\n", "    print(\"Missing Data Analysis:\")\n", "    missing_stats = clean_df.isnull().sum()\n", "    for col, missing in missing_stats.items():\n", "        if missing > 0:\n", "            pct = (missing / len(clean_df)) * 100\n", "            print(f\"  {col}: {missing} ({pct:.1f}%)\")\n", "    \n", "    # Currency distribution\n", "    print(f\"\\nCurrency Distribution:\")\n", "    currency_counts = clean_df['currency'].value_counts()\n", "    for currency, count in currency_counts.head(10).items():\n", "        pct = (count / len(clean_df)) * 100\n", "        print(f\"  {currency}: {count} events ({pct:.1f}%)\")\n", "    \n", "    # Events with actual vs forecast data\n", "    has_actual = (clean_df['actual'].notna() & (clean_df['actual'] != '')).sum()\n", "    has_forecast = (clean_df['forecast'].notna() & (clean_df['forecast'] != '')).sum()\n", "    has_both = ((clean_df['actual'].notna() & (clean_df['actual'] != '')) & \n", "                (clean_df['forecast'].notna() & (clean_df['forecast'] != ''))).sum()\n", "    \n", "    print(f\"\\nData Completeness:\")\n", "    print(f\"  Events with actual values: {has_actual} ({has_actual/len(clean_df)*100:.1f}%)\")\n", "    print(f\"  Events with forecast values: {has_forecast} ({has_forecast/len(clean_df)*100:.1f}%)\")\n", "    print(f\"  Events with both actual & forecast: {has_both} ({has_both/len(clean_df)*100:.1f}%)\")\n", "    \n", "    # Timeline analysis\n", "    clean_df['date'] = clean_df['timestamp'].dt.date\n", "    daily_events = clean_df.groupby('date').size()\n", "    \n", "    print(f\"\\nTimeline Analysis:\")\n", "    print(f\"  Total days with events: {len(daily_events)}\")\n", "    print(f\"  Average events per day: {daily_events.mean():.1f}\")\n", "    print(f\"  Busiest day: {daily_events.idxmax()} ({daily_events.max()} events)\")\n", "    \n", "else:\n", "    print(\"❌ No clean data available for analysis\")\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "💾 STEP 5: Save Consolidated Dataset\n", "----------------------------------------\n", "✅ Saved consolidated dataset:\n", "   Main: ..\\data\\news\\processed\\consolidated_events_20250706_154235.csv (452 events)\n", "   Detailed: ..\\data\\news\\processed\\detailed_events_20250706_154235.csv (452 events with source tracking)\n", "\n", "📈 Final Dataset Summary:\n", "   total_events: 452\n", "   unique_currencies: 8\n", "   date_range_start: 2024-01-03 20:45:00\n", "   date_range_end: 2024-06-30 14:15:00\n", "   events_with_actuals: 350\n", "   events_with_forecasts: 344\n", "   events_with_both: 344\n", "   processing_timestamp: 20250706_154235\n"]}], "source": ["# 5. SAVE CONSOLIDATED DATASET\n", "print(\"\\n💾 STEP 5: Save Consolidated Dataset\")\n", "print(\"-\" * 40)\n", "\n", "if 'clean_df' in locals() and len(clean_df) > 0:\n", "    # Generate filename with timestamp\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    output_file = PROCESSED_DATA_DIR / f'consolidated_events_{timestamp}.csv'\n", "    \n", "    # Save main dataset (drop helper columns)\n", "    main_columns = ['timestamp', 'currency', 'event', 'impact', 'actual', 'forecast', 'previous', 'scraped_at']\n", "    final_df = clean_df[main_columns].copy()\n", "    \n", "    # Save to CSV\n", "    final_df.to_csv(output_file, index=False)\n", "    \n", "    # Also save detailed analysis with source tracking\n", "    detailed_file = PROCESSED_DATA_DIR / f'detailed_events_{timestamp}.csv'\n", "    clean_df.to_csv(detailed_file, index=False)\n", "    \n", "    print(f\"✅ Saved consolidated dataset:\")\n", "    print(f\"   Main: {output_file} ({len(final_df)} events)\")\n", "    print(f\"   Detailed: {detailed_file} ({len(clean_df)} events with source tracking)\")\n", "    \n", "    # Save summary statistics\n", "    summary_stats = {\n", "        'total_events': len(final_df),\n", "        'unique_currencies': final_df['currency'].nunique(),\n", "        'date_range_start': str(final_df['timestamp'].min()),\n", "        'date_range_end': str(final_df['timestamp'].max()),\n", "        'events_with_actuals': int(has_actual),\n", "        'events_with_forecasts': int(has_forecast),\n", "        'events_with_both': int(has_both),\n", "        'processing_timestamp': timestamp\n", "    }\n", "    \n", "    print(f\"\\n📈 Final Dataset Summary:\")\n", "    for key, value in summary_stats.items():\n", "        print(f\"   {key}: {value}\")\n", "        \n", "else:\n", "    print(\"❌ No data to save\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 🎯 Next Steps\n", "\n", "After running this notebook:\n", "\n", "1. **Review Data Quality** - Check the analysis output above\n", "2. **Validate Coverage** - Ensure all expected weeks are present  \n", "3. **Check for Gaps** - Identify any missing time periods\n", "4. **Proceed to Phase 2** - Use the consolidated dataset for sentiment labeling\n", "\n", "## 📝 Usage Instructions\n", "\n", "```bash\n", "# 1. Ensure batch scraper has completed\n", "python scripts/monitor_progress.py\n", "\n", "# 2. Run this notebook\n", "jupyter notebook notebooks/data_analysis.ipynb\n", "\n", "# 3. Execute all cells to generate consolidated dataset\n", "```\n", "\n", "The notebook will create:\n", "- `consolidated_events_YYYYMMDD_HHMMSS.csv` - Clean dataset for Phase 2\n", "- `detailed_events_YYYYMMDD_HHMMSS.csv` - Dataset with source file tracking\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}