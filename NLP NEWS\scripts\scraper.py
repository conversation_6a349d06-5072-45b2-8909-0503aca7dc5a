"""
Final ForexFactory Economic Calendar Scraper

This version uses JavaScript execution to extract calendar data directly
from the page's JavaScript context, avoiding JSON parsing issues.
"""

import os
import sys
import time
import random
import logging
import yaml
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager


class ForexFactoryScraper:
    """
    Final scraper that extracts data using JavaScript execution.
    """
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """Initialize the scraper with configuration."""
        self.config = self._load_config(config_path)
        self.logger = self._setup_logging()
        self.driver = None
        self.session_start = datetime.now()
        
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            # Default configuration if file not found
            return {
                'scraping': {
                    'delay_between_requests': 2.5,
                    'request_timeout': 30,
                    'max_retries': 3,
                    'retry_delay': 1,
                    'headless': True,
                    'window_size': [1920, 1080],
                    'user_agents': [
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                    ]
                },
                'data_validation': {
                    'required_fields': ["timestamp", "currency", "event", "impact"],
                    'high_impact_only': True,
                    'allowed_currencies': ["USD", "EUR", "GBP", "JPY", "CAD", "AUD", "NZD", "CHF"]
                },
                'logging': {
                    'level': 'INFO',
                    'log_to_file': True,
                    'log_file': 'logs/scraper.log'
                },
                'output': {
                    'format': 'csv',
                    'data_directory': 'data/news/raw'
                },
                'forexfactory': {
                    'base_url': 'https://www.forexfactory.com/calendar',
                    'timezone': 'UTC'
                }
            }
    
    def _setup_logging(self) -> logging.Logger:
        """Setup comprehensive logging."""
        logger = logging.getLogger('ForexFactoryScraper')
        logger.setLevel(getattr(logging, self.config['logging']['level']))
        
        # Clear any existing handlers
        logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # File handler (if enabled)
        if self.config['logging']['log_to_file']:
            os.makedirs(os.path.dirname(self.config['logging']['log_file']), exist_ok=True)
            file_handler = logging.FileHandler(self.config['logging']['log_file'])
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def _setup_driver(self) -> webdriver.Chrome:
        """Setup Chrome WebDriver with optimal settings."""
        self.logger.info("Setting up Chrome WebDriver...")
        
        chrome_options = Options()
        
        # Basic options
        if self.config['scraping']['headless']:
            chrome_options.add_argument('--headless')
        
        chrome_options.add_argument(f"--window-size={','.join(map(str, self.config['scraping']['window_size']))}")
        
        # Anti-detection options
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Random user agent
        user_agent = random.choice(self.config['scraping']['user_agents'])
        chrome_options.add_argument(f'--user-agent={user_agent}')
        self.logger.info(f"Using user agent: {user_agent}")
        
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Execute script to remove webdriver property
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info("Chrome WebDriver setup successful")
            return driver
            
        except Exception as e:
            self.logger.error(f"Failed to setup Chrome WebDriver: {e}")
            raise
    
    def _rate_limit(self):
        """Apply rate limiting between requests."""
        delay = self.config['scraping']['delay_between_requests']
        # Add some randomness to avoid pattern detection
        actual_delay = delay + random.uniform(0, 1)
        self.logger.debug(f"Rate limiting: sleeping for {actual_delay:.2f} seconds")
        time.sleep(actual_delay)
    
    def scrape_week(self, week_string: str) -> pd.DataFrame:
        """
        Scrape economic events for a specific week.
        
        Args:
            week_string: Week identifier (e.g., 'jan1.2024')
            
        Returns:
            DataFrame with economic events
        """
        self.logger.info(f"Starting scrape for week: {week_string}")
        
        try:
            # Setup driver
            self.driver = self._setup_driver()
            
            # Build URL
            url = f"{self.config['forexfactory']['base_url']}?week={week_string}"
            self.logger.info(f"Scraping URL: {url}")
            
            # Load page
            self.driver.get(url)
            
            # Wait for page to load
            time.sleep(5)
            
            # Apply rate limiting
            self._rate_limit()
            
            # Extract calendar data using JavaScript execution
            events_data = self._extract_calendar_data_js()
            
            # Validate and clean data
            validated_data = self._validate_data(events_data)
            
            self.logger.info(f"Successfully scraped {len(validated_data)} events for week {week_string}")
            return validated_data
            
        except Exception as e:
            self.logger.error(f"Failed to scrape week {week_string}: {e}")
            raise
        finally:
            if self.driver:
                self.driver.quit()
                self.logger.info("WebDriver closed")
    
    def _extract_calendar_data_js(self) -> List[Dict]:
        """Extract calendar data using JavaScript execution."""
        try:
            if not self.driver:
                self.logger.error("WebDriver not initialized")
                return []
            
            # Wait for calendar data to be available
            wait_script = """
            return typeof window.calendarComponentStates !== 'undefined' && 
                   window.calendarComponentStates[1] && 
                   window.calendarComponentStates[1].days;
            """
            
            # Wait up to 10 seconds for data to load
            for _ in range(10):
                if self.driver.execute_script(wait_script):
                    break
                time.sleep(1)
            else:
                self.logger.error("Calendar data not found after waiting")
                return []
            
            # Extract events using JavaScript
            extract_script = """
            var events = [];
            if (window.calendarComponentStates && window.calendarComponentStates[1] && window.calendarComponentStates[1].days) {
                var days = window.calendarComponentStates[1].days;
                for (var i = 0; i < days.length; i++) {
                    var day = days[i];
                    if (day.events) {
                        for (var j = 0; j < day.events.length; j++) {
                            var event = day.events[j];
                            events.push({
                                name: event.name || '',
                                currency: event.currency || '',
                                dateline: event.dateline || 0,
                                impactClass: event.impactClass || '',
                                actual: event.actual || '',
                                forecast: event.forecast || '',
                                previous: event.previous || '',
                                date: day.date || ''
                            });
                        }
                    }
                }
            }
            return events;
            """
            
            events_data = self.driver.execute_script(extract_script)
            self.logger.info(f"Extracted {len(events_data)} events using JavaScript")
            
            # Process events
            processed_events = []
            for event in events_data:
                processed_event = self._process_event_data(event)
                if processed_event:
                    processed_events.append(processed_event)
            
            return processed_events
            
        except Exception as e:
            self.logger.error(f"Failed to extract calendar data: {e}")
            return []
    
    def _process_event_data(self, event: Dict) -> Optional[Dict]:
        """Process a single event from the extracted data."""
        try:
            # Check if high impact only
            impact_class = event.get('impactClass', '')
            is_high_impact = 'red' in impact_class
            
            if self.config['data_validation']['high_impact_only'] and not is_high_impact:
                return None
            
            # Extract basic fields
            currency = event.get('currency', '')
            event_name = event.get('name', '')
            
            # Filter by allowed currencies
            if currency not in self.config['data_validation']['allowed_currencies']:
                return None
            
            # Skip if essential data is missing
            if not event_name or not currency:
                return None
            
            # Create timestamp from dateline (Unix timestamp)
            dateline = event.get('dateline', 0)
            if dateline and dateline > 0:
                timestamp = datetime.fromtimestamp(dateline).isoformat()
            else:
                timestamp = datetime.now().isoformat()
            
            # Determine impact level
            if 'red' in impact_class:
                impact = 'High'
            elif 'ora' in impact_class:
                impact = 'Medium'
            else:
                impact = 'Low'
            
            return {
                'timestamp': timestamp,
                'currency': currency,
                'event': event_name,
                'impact': impact,
                'actual': event.get('actual', ''),
                'forecast': event.get('forecast', ''),
                'previous': event.get('previous', ''),
                'scraped_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.warning(f"Failed to process event: {e}")
            return None
    
    def _validate_data(self, events: List[Dict]) -> pd.DataFrame:
        """Validate and clean the scraped data."""
        if not events:
            self.logger.warning("No events to validate")
            return pd.DataFrame()
        
        df = pd.DataFrame(events)
        initial_count = len(df)
        
        # Remove duplicates
        df = df.drop_duplicates(subset=['timestamp', 'currency', 'event'])
        self.logger.info(f"Removed {initial_count - len(df)} duplicate events")
        
        # Validate required fields
        required_fields = self.config['data_validation']['required_fields']
        for field in required_fields:
            if field not in df.columns:
                self.logger.warning(f"Required field '{field}' missing from data")
        
        # Sort by timestamp
        df = df.sort_values('timestamp')
        
        self.logger.info(f"Data validation complete: {len(df)} valid events")
        return df
    
    def save_data(self, df: pd.DataFrame, week_string: str) -> str:
        """Save scraped data to file."""
        if df.empty:
            self.logger.warning("No data to save")
            return ""
        
        # Create output directory
        output_dir = self.config['output']['data_directory']
        os.makedirs(output_dir, exist_ok=True)
        
        # Create filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"forexfactory_{week_string}_{timestamp}.csv"
        filepath = os.path.join(output_dir, filename)
        
        # Save data
        df.to_csv(filepath, index=False)
        self.logger.info(f"Data saved to: {filepath}")
        
        return filepath


def main():
    """Main function to run the final scraper."""
    import argparse
    
    parser = argparse.ArgumentParser(description='ForexFactory Economic Calendar Scraper')
    parser.add_argument('week', help='Week to scrape (e.g., jan1.2024)')
    parser.add_argument('--config', default='config/settings.yaml', help='Configuration file path')
    
    args = parser.parse_args()
    
    try:
        # Initialize scraper
        scraper = ForexFactoryScraper(args.config)
        
        # Scrape data
        df = scraper.scrape_week(args.week)
        
        # Save data
        filepath = scraper.save_data(df, args.week)
        
        print(f"Successfully scraped {len(df)} events for week {args.week}")
        print(f"Data saved to: {filepath}")
        
        # Display sample data
        if not df.empty:
            print("\nSample data:")
            print(df.head())
        
    except Exception as e:
        print(f"Scraping failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 