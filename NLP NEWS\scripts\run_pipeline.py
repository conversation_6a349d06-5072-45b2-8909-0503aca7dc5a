#!/usr/bin/env python3
"""
Complete Data Processing Pipeline
Runs the full data collection, consolidation, and sentiment analysis pipeline.
"""

import argparse
import sys
import subprocess
from pathlib import Path
import logging

def setup_logging():
    """Setup logging for the pipeline."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def run_command(command, description, logger):
    """Run a command and handle errors."""
    logger.info(f"🚀 {description}")
    logger.info(f"Command: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        logger.info(f"✅ {description} completed successfully")
        if result.stdout:
            logger.info(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed")
        logger.error(f"Error: {e.stderr}")
        return False

def check_files_exist(files, logger):
    """Check if required files exist."""
    missing_files = []
    for file_path in files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ Missing required files: {missing_files}")
        return False
    
    logger.info(f"✅ All required files exist")
    return True

def main():
    """Main pipeline function."""
    parser = argparse.ArgumentParser(description='Run complete data processing pipeline')
    parser.add_argument('--skip-scraping', action='store_true', 
                       help='Skip data scraping (use existing raw data)')
    parser.add_argument('--skip-consolidation', action='store_true',
                       help='Skip data consolidation (use existing consolidated data)')
    parser.add_argument('--config', default='config/settings.yaml',
                       help='Configuration file path')
    
    args = parser.parse_args()
    logger = setup_logging()
    
    logger.info("🎯 Starting Complete Data Processing Pipeline")
    logger.info("=" * 60)
    
    # Step 1: Data Scraping (optional)
    if not args.skip_scraping:
        logger.info("📡 STEP 1: Data Scraping")
        logger.info("-" * 40)
        
        # Check if we should run batch scraper
        raw_dir = Path('data/news/raw')
        if raw_dir.exists() and list(raw_dir.glob('forexfactory_*.csv')):
            logger.info("Raw data files already exist. Use --skip-scraping to skip this step.")
            response = input("Continue with scraping? (y/N): ").lower()
            if response != 'y':
                logger.info("Skipping data scraping")
                args.skip_scraping = True
        
        if not args.skip_scraping:
            # Run batch scraper with consolidation
            command = [
                'python', 'scripts/batch_scraper.py',
                '--config', args.config,
                '--consolidate'
            ]
            
            if not run_command(command, "Batch scraping and consolidation", logger):
                logger.error("Pipeline failed at scraping step")
                return 1
    
    # Step 2: Data Consolidation (optional)
    if not args.skip_consolidation and args.skip_scraping:
        logger.info("📊 STEP 2: Data Consolidation")
        logger.info("-" * 40)
        
        # Check if raw files exist
        raw_dir = Path('data/news/raw')
        if not raw_dir.exists() or not list(raw_dir.glob('forexfactory_*.csv')):
            logger.error("No raw data files found. Run scraping first.")
            return 1
        
        # Run consolidation only
        command = [
            'python', '-c',
            '''
from scripts.batch_scraper import BatchForexFactoryScraper
scraper = BatchForexFactoryScraper("config/settings.yaml")
main_file, detailed_file = scraper.consolidate_data()
print(f"Consolidated to: {main_file}, {detailed_file}")
            '''
        ]
        
        if not run_command(command, "Data consolidation", logger):
            logger.error("Pipeline failed at consolidation step")
            return 1
    
    # Step 3: Check consolidated data exists
    logger.info("🔍 STEP 3: Verify Consolidated Data")
    logger.info("-" * 40)
    
    required_files = [
        'data/news/processed/economic_events_raw.csv',
        'data/news/processed/economic_events_detailed.csv'
    ]
    
    if not check_files_exist(required_files, logger):
        logger.error("Consolidated data files missing. Run consolidation first.")
        return 1
    
    # Step 4: Sentiment Analysis
    logger.info("🧠 STEP 4: Sentiment Analysis")
    logger.info("-" * 40)
    
    command = [
        'python', 'scripts/sentiment_labeler.py',
        '--input', 'data/news/processed/economic_events_raw.csv'
    ]
    
    if not run_command(command, "Sentiment analysis", logger):
        logger.error("Pipeline failed at sentiment analysis step")
        return 1
    
    # Step 5: Final Verification
    logger.info("✅ STEP 5: Final Verification")
    logger.info("-" * 40)
    
    final_files = [
        'data/news/processed/economic_events_raw.csv',
        'data/news/processed/economic_events_detailed.csv',
        'data/news/processed/economic_events_labeled.csv',
        'data/news/processed/sentiment_analysis_report.yaml'
    ]
    
    if check_files_exist(final_files, logger):
        logger.info("🎉 Pipeline completed successfully!")
        logger.info("📁 Output files:")
        for file_path in final_files:
            file_size = Path(file_path).stat().st_size / 1024
            logger.info(f"   - {file_path} ({file_size:.1f} KB)")
        
        # Run monitor to show final status
        logger.info("\n📊 Final Status:")
        subprocess.run(['python', 'scripts/monitor_progress.py'])
        
        return 0
    else:
        logger.error("❌ Pipeline completed with missing files")
        return 1

if __name__ == "__main__":
    sys.exit(main())
