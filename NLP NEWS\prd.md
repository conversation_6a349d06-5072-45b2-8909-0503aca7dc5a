# 📊 Objective: Build a Backtestable High-Impact News Trading Strategy

## 🧠 High-Level Goal

Develop a **backtestable trading strategy** for gold and forex markets (e.g., XAU/USD, EUR/USD) that triggers entries based on **high-impact macroeconomic news events** and **price action confirmation**. The system should allow you to evaluate historical performance of such strategies using structured economic calendar data and candle price data.

---

## ✅ End Outcome

A pipeline that allows:

1. **Scraping backdated high-impact economic news** from sources like ForexFactory
2. **Labeling news events as bullish or bearish**
3. **Loading 1-minute candle price data (from Dukascopy or similar)**
4. **Simulating trades triggered by news + price confirmation**
5. **Generating performance metrics (e.g., win rate, PnL, drawdown, Sharpe, etc.)**

---

## 👱️ Phase-by-Phase Tasks

### 📦 PHASE 1: Scrape Historical High-Impact News

**Goal:** Extract high-impact economic events from [ForexFactory.com](https://www.forexfactory.com/calendar) using a headless browser (Selenium).

**Deliverables:**

* A Python function to scrape a specific week (e.g., `jan1.2024`) from ForexFactory
* Fields to extract:

  * Timestamp (UTC)
  * Currency (USD, EUR, etc.)
  * Event name (e.g., Non-Farm Payrolls)
  * Impact (only "High")
  * Actual / Forecast / Previous values
* Output format: `CSV` or `JSON` (for backtesting)

**🔧 Robustness Features:**
* Rate limiting (2-3 second delays between requests)
* User-agent rotation to avoid detection
* Retry logic for failed requests (3 attempts with exponential backoff)
* Request timeout handling
* Logging for debugging and monitoring

> Tooling: `Selenium`, `webdriver-manager`, `pandas`, `logging`

---

### 🧠 PHASE 2: Label Sentiment from News

**Goal:** Use economic data deltas (Actual vs. Forecast) to tag each event as bullish, bearish, or neutral for a given asset.

**Examples:**

* Better-than-expected NFP → bullish for USD → **bearish for gold**
* Hotter-than-expected CPI → **bearish for gold**, bullish for USD
* Dovish Fed statement → **bullish for gold**, bearish for USD

**Deliverables:**

* Rule-based classifier with configurable rules (YAML config file)
* Data quality validation:
  * Handle missing actual/forecast values
  * Filter out revised/preliminary data
  * Validate timestamp formats
* Output format:

  * `event_sentiment`: bullish, bearish, neutral
  * `asset_impacted`: XAU/USD, EUR/USD, etc.
  * `confidence_score`: 0.0-1.0 based on data quality

---

### 📈 PHASE 3: Load Historical Candle Price Data

**Goal:** Load 1-minute OHLCV data for target assets (e.g., XAU/USD) that aligns with the event timestamps.

**Deliverables:**

* A data loader for CSV-based 1-minute price data
* Timezone consistency verification (align everything to UTC)
* Handle missing data/holidays gracefully
* Provide functions to extract N-minute window post-event (e.g., 30 min)

---

### ⚔️ PHASE 4: Strategy Backtest Engine

**Goal:** Simulate trades triggered by:

* A bullish/bearish event
* Confirmation by price action (e.g., breakout candle, RSI oversold, etc.)

**Strategy Example:**

* On a **bullish gold signal**:

  * If XAU/USD closes above last 30-min high within 5 minutes of news → enter long
  * Exit on 0.5% gain, RSI > 70, or 30 min timeout

**Deliverables:**

* Backtester engine:

  * Simulate trades using news + price
  * Add realistic slippage/spread assumptions
  * Track entry/exit time, PnL
* Output:

  * Equity curve
  * Trade log
  * Performance stats: Win rate, Sharpe, Max Drawdown, Profit Factor

---

### 📊 PHASE 5: Analytics + Refinement

**Goal:** Visualize and analyze how the strategy performs over time

**Deliverables:**

* Charts:

  * Equity curve
  * Drawdown plot
  * Trade distribution
* CSV summary report
* Optional: Streamlit or Jupyter UI to adjust thresholds and visualize impact

---

## 🧠 Optional Improvements Later

* Integrate AI classifier for event sentiment using `transformers`
* Add live pipeline (scrape real-time news and trigger trades)
* Add multiple assets and risk-adjusted position sizing
* **Backup data sources:** TradingView Economic Calendar, Investing.com
* **Advanced sentiment analysis:** NLP on news descriptions
* **ML enhancements:** Anomaly detection for unusual market reactions

---

## 🧪 Data Sources Summary

| Type        | Source                          | Format           | Backup Source              |
| ----------- | ------------------------------- | ---------------- | -------------------------- |
| News Events | ForexFactory Economic Calendar  | Web-scraped HTML | TradingView Economic Cal   |
| Price Data  | Dukascopy, MT5 exports, etc.    | CSV (1m OHLCV)   | Yahoo Finance, Alpha Vantage |
| Sentiment   | Rule-based from forecast/actual | Tagged manually  | -                          |

---

## 📁 Enhanced Folder Structure

```bash
backtest-news-strategy/
├── data/
│   ├── news/
│   │   ├── raw/                # Raw scraped data
│   │   ├── processed/          # Cleaned & labeled data
│   │   └── cache/              # Cached responses
│   └── prices/                 # OHLCV data
├── scripts/
│   ├── scraper.py              # Selenium-based ForexFactory scraper
│   ├── data_validator.py       # Data quality validation
│   ├── sentiment_labeler.py    # Classify economic event polarity
│   ├── backtest.py             # Core backtesting engine
│   └── analysis.py             # Plotting & performance analysis
├── config/
│   ├── scraping_rules.yaml     # Sentiment classification rules
│   ├── assets.yaml             # Asset configuration
│   └── settings.yaml           # General settings
├── logs/                       # Scraping and error logs
├── tests/                      # Unit tests
├── notebooks/
│   └── strategy_demo.ipynb
├── requirements.txt
└── README.md
```

---

## 🔧 Data Quality & Error Handling

### **Scraping Robustness:**
* **Rate Limiting:** 2-3 second delays between requests
* **User-Agent Rotation:** Avoid bot detection
* **Retry Logic:** 3 attempts with exponential backoff
* **Timeout Handling:** 30-second request timeouts
* **Comprehensive Logging:** Track all scraping activities

### **Data Validation:**
* **Timestamp Validation:** Ensure UTC consistency
* **Missing Data Handling:** Flag incomplete records
* **Duplicate Detection:** Remove duplicate events
* **Holiday/Weekend Filtering:** Handle market closures
* **Data Completeness Checks:** Validate all required fields

---

## 🧠 Developer Notes

* **Timezone consistency** (align everything to UTC)
* **Handle missing data/holidays** gracefully
* **Add realistic slippage/spread assumptions**
* **Keep system modular** (can expand later to ML/NLP)
* **Comprehensive logging** for debugging
* **Unit tests** for critical functions
* **Configuration-driven** (YAML files for easy adjustments)

---

## ✅ First Actionable Task

> Build `scraper.py` that uses **Selenium** to extract all **high-impact** news from ForexFactory for a given week, with robust error handling and data validation.

### **Implementation Steps:**
1. **Setup project structure** with enhanced folder layout
2. **Create requirements.txt** with all necessary dependencies
3. **Build basic scraper** with Selenium WebDriver
4. **Add robustness features** (rate limiting, retries, logging)
5. **Implement data validation** and quality checks
6. **Test with sample data** and validate output format
