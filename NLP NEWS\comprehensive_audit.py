#!/usr/bin/env python3
"""Comprehensive audit of both sentiment analysis systems."""

import pandas as pd
import yaml
import sys
import os
from pathlib import Path

# Add the scripts directory to the path
sys.path.append('scripts')
sys.path.append('Codebase')

def load_config(config_path):
    """Load configuration file."""
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)

def analyze_threshold_consistency():
    """Check threshold consistency across both systems."""
    print("=== THRESHOLD CONSISTENCY ANALYSIS ===")
    
    our_config = load_config('config/sentiment_rules.yaml')
    their_config = load_config('Codebase/sentiment_rules.yaml')
    
    print("\n1. CATEGORY THRESHOLDS:")
    our_thresholds = our_config['processing_settings']['category_thresholds']
    their_thresholds = their_config['processing_settings']['category_thresholds']
    
    for category in our_thresholds:
        our_val = our_thresholds[category]
        their_val = their_thresholds.get(category, 'MISSING')
        match = "✅" if our_val == their_val else "❌"
        print(f"  {category}: Our={our_val}% | Their={their_val}% {match}")
    
    print("\n2. RULE CONDITION THRESHOLDS:")
    # Check inflation rules
    our_inflation = our_config['quantitative_events']['inflation']['sentiment_rules']
    their_inflation = their_config['quantitative_events']['inflation']['sentiment_rules']
    
    print("  Inflation Rules:")
    for rule_name in our_inflation:
        if rule_name in their_inflation:
            our_conditions = our_inflation[rule_name].get('conditions', [])
            their_conditions = their_inflation[rule_name].get('conditions', [])
            print(f"    {rule_name}:")
            print(f"      Our: {our_conditions}")
            print(f"      Their: {their_conditions}")
            match = "✅" if our_conditions == their_conditions else "❌"
            print(f"      Match: {match}")

def analyze_economic_logic():
    """Analyze the economic logic of sentiment rules."""
    print("\n=== ECONOMIC LOGIC ANALYSIS ===")
    
    our_config = load_config('config/sentiment_rules.yaml')
    
    print("\n1. INFLATION LOGIC VALIDATION:")
    inflation_rules = our_config['quantitative_events']['inflation']['sentiment_rules']
    
    # Check if inflation logic is correct for current monetary environment
    hotter = inflation_rules.get('hotter_than_expected', {})
    cooler = inflation_rules.get('cooler_than_expected', {})
    
    print("  Higher Inflation (hotter_than_expected):")
    print(f"    USD: {hotter.get('usd_sentiment')} (Should be bullish - rate hikes)")
    print(f"    Gold: {hotter.get('gold_sentiment')} (Should be bearish - USD strength)")
    
    print("  Lower Inflation (cooler_than_expected):")
    print(f"    USD: {cooler.get('usd_sentiment')} (Should be bearish - rate cuts)")
    print(f"    Gold: {cooler.get('gold_sentiment')} (Should be bullish - USD weakness)")
    
    # Validate logic
    inflation_logic_correct = (
        hotter.get('usd_sentiment') == 'bullish' and 
        hotter.get('gold_sentiment') == 'bearish' and
        cooler.get('usd_sentiment') == 'bearish' and 
        cooler.get('gold_sentiment') == 'bullish'
    )
    print(f"  Inflation Logic: {'✅ CORRECT' if inflation_logic_correct else '❌ INCORRECT'}")
    
    print("\n2. EMPLOYMENT LOGIC VALIDATION:")
    employment_rules = our_config['quantitative_events']['employment']['sentiment_rules']
    
    stronger = employment_rules.get('stronger_than_expected', {})
    weaker = employment_rules.get('weaker_than_expected', {})
    
    print("  Better Employment (stronger_than_expected):")
    print(f"    USD: {stronger.get('usd_sentiment')} (Should be bullish - strong economy)")
    print(f"    Gold: {stronger.get('gold_sentiment')} (Should be bearish - USD strength)")
    
    employment_logic_correct = (
        stronger.get('usd_sentiment') == 'bullish' and 
        stronger.get('gold_sentiment') == 'bearish'
    )
    print(f"  Employment Logic: {'✅ CORRECT' if employment_logic_correct else '❌ INCORRECT'}")

def analyze_special_cases():
    """Analyze special case handling."""
    print("\n=== SPECIAL CASES ANALYSIS ===")
    
    # Test unemployment rate special case
    print("\n1. UNEMPLOYMENT RATE SPECIAL CASE:")
    print("  Economic Logic: Higher unemployment = Bad for economy = USD bearish")
    print("  System should treat unemployment as INVERSE indicator")
    
    # Test PMI logic
    print("\n2. PMI LOGIC:")
    our_config = load_config('config/sentiment_rules.yaml')
    pmi_rules = our_config['quantitative_events']['business_activity']['sentiment_rules']
    
    expansion = pmi_rules.get('expansion_stronger', {})
    contraction = pmi_rules.get('contraction_deeper', {})
    
    print("  PMI Expansion (>50, better than forecast):")
    print(f"    Conditions: {expansion.get('conditions', [])}")
    print(f"    USD: {expansion.get('usd_sentiment')}")
    
    print("  PMI Contraction (<50, worse than forecast):")
    print(f"    Conditions: {contraction.get('conditions', [])}")
    print(f"    USD: {contraction.get('usd_sentiment')}")

def analyze_currency_correlation():
    """Analyze currency correlation logic."""
    print("\n=== CURRENCY CORRELATION ANALYSIS ===")
    
    print("\n1. USD-GOLD INVERSE CORRELATION:")
    print("  Rule: USD strength should = Gold weakness")
    print("  Implementation: Checked in _apply_asset_correlation_validation")
    
    print("\n2. EUR/GBP SENTIMENT MAPPING:")
    print("  EUR events should affect EUR pairs")
    print("  GBP events should affect GBP pairs")
    print("  Cross-currency effects should be logical")

def test_edge_cases():
    """Test edge cases and potential bugs."""
    print("\n=== EDGE CASES & POTENTIAL BUGS ===")
    
    print("\n1. ZERO FORECAST HANDLING:")
    print("  What happens when forecast = 0? Division by zero risk?")
    
    print("\n2. MISSING DATA HANDLING:")
    print("  How are null/missing actual or forecast values handled?")
    
    print("\n3. PERCENTAGE VS DECIMAL CONFUSION:")
    print("  Are thresholds consistently in % or decimal format?")
    
    print("\n4. RULE PRIORITY CONFLICTS:")
    print("  What if multiple rules could apply to same event?")

def main():
    """Run comprehensive audit."""
    print("🔍 COMPREHENSIVE SENTIMENT SYSTEM AUDIT")
    print("=" * 50)
    
    analyze_threshold_consistency()
    analyze_economic_logic()
    analyze_special_cases()
    analyze_currency_correlation()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("📋 AUDIT COMPLETE - Review findings above")

if __name__ == "__main__":
    main()
