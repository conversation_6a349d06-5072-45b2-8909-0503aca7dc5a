#!/usr/bin/env python3
"""Debug script to test German CPI sentiment classification."""

import pandas as pd
import yaml
import sys
import os

# Add the scripts directory to the path
sys.path.append('scripts')

from sentiment_labeler import <PERSON>timent<PERSON>abe<PERSON>

def main():
    # Initialize the sentiment labeler
    labeler = SentimentLabeler('config/sentiment_rules.yaml')
    
    # Create test data for German CPI
    test_data = {
        'timestamp': '2024-01-04 13:14:00',
        'currency': 'EUR',
        'event': 'German Prelim CPI m/m',
        'importance': 'High',
        'actual': '0.1%',
        'forecast': '0.2%',
        'previous': '-0.4%'
    }
    
    # Convert to pandas Series
    row = pd.Series(test_data)
    
    print("=== GERMAN CPI DEBUG ===")
    print(f"Event: {row['event']}")
    print(f"Currency: {row['currency']}")
    print(f"Actual: {row['actual']}")
    print(f"Forecast: {row['forecast']}")
    print()
    
    # Test parsing
    actual_parsed = labeler._parse_numeric_value(row['actual'])
    forecast_parsed = labeler._parse_numeric_value(row['forecast'])
    print(f"Parsed actual: {actual_parsed}")
    print(f"Parsed forecast: {forecast_parsed}")
    
    # Test surprise calculation
    surprise_pct, surprise_direction = labeler._calculate_surprise(actual_parsed, forecast_parsed, row['event'])
    print(f"Surprise: {surprise_pct}% ({surprise_direction})")
    
    # Test category mapping
    category = labeler._quantitative_events.get(row['event'])
    print(f"Category: {category}")
    
    # Test threshold
    if category:
        threshold = labeler._get_category_threshold(category)
        print(f"Threshold: {threshold}%")
        print(f"Surprise > Threshold: {surprise_pct > threshold}")
    
    # Test rule availability
    if category:
        category_config = labeler.config['quantitative_events'][category]
        rules = category_config['sentiment_rules']
        print(f"Available rules: {list(rules.keys())}")
        print(f"Has 'cooler_than_expected': {'cooler_than_expected' in rules}")
        if 'cooler_than_expected' in rules:
            print(f"cooler_than_expected rule: {rules['cooler_than_expected']}")

    # Test rule classification
    result = labeler.classify_event(row)
    print()
    print("=== CLASSIFICATION RESULT ===")
    for key, value in result.items():
        print(f"{key}: {value}")

if __name__ == "__main__":
    main()
