#!/usr/bin/env python3
"""Test if their system actually evaluates conditions."""

import pandas as pd
import yaml
import sys
import os

# Add the scripts directory to the path
sys.path.append('scripts')
sys.path.append('Codebase')

# Import both systems
from sentiment_labeler import <PERSON>timentLabeler as OurSystem
import importlib.util
spec = importlib.util.spec_from_file_location("their_system", "Codebase/sentiment_labeler.py")
their_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(their_module)
TheirSystem = their_module.SentimentLabeler

def test_condition_evaluation():
    """Test if conditions are actually evaluated."""
    print("=== CONDITION EVALUATION TEST ===")
    
    # Initialize both systems
    our_system = OurSystem('config/sentiment_rules.yaml')
    their_system = TheirSystem('Codebase/sentiment_rules.yaml')
    
    # Create a test case that should NOT trigger based on conditions
    # German CPI: actual=0.3%, forecast=0.2% (positive surprise)
    # But let's make the surprise very small (50% but less than threshold in their config)
    test_data = {
        'timestamp': '2024-01-04 13:14:00',
        'currency': 'EUR',
        'event': 'German Prelim CPI m/m',
        'importance': 'High',
        'actual': '0.21%',  # Very small increase
        'forecast': '0.2%',
        'previous': '-0.4%'
    }
    
    row = pd.Series(test_data)
    
    print(f"Test Case: {row['event']}")
    print(f"Actual: {row['actual']} | Forecast: {row['forecast']}")
    
    # Calculate surprise manually
    actual_val = 0.21
    forecast_val = 0.2
    surprise_pct = ((actual_val - forecast_val) / abs(forecast_val)) * 100
    print(f"Surprise: {surprise_pct:.1f}% (positive)")
    
    # Test our system
    our_result = our_system.classify_event(row)
    print(f"\nOUR SYSTEM:")
    print(f"  USD: {our_result['usd_sentiment']}")
    print(f"  Gold: {our_result['gold_sentiment']}")
    print(f"  Confidence: {our_result['confidence']}")
    print(f"  Reason: {our_result['reason']}")
    
    # Test their system
    their_result = their_system.classify_event(row)
    print(f"\nTHEIR SYSTEM:")
    print(f"  USD: {their_result['usd_sentiment']}")
    print(f"  Gold: {their_result['gold_sentiment']}")
    print(f"  Confidence: {their_result['confidence']}")
    print(f"  Reason: {their_result['reason']}")
    
    # Analysis
    print(f"\n=== ANALYSIS ===")
    print(f"Surprise {surprise_pct:.1f}% vs 0.05% threshold")
    
    # Check if their conditions would be met
    print(f"\nTheir conditions for 'hotter_than_expected':")
    print(f"  'actual > forecast': {actual_val > forecast_val} ✅")
    print(f"  'positive_surprise > 0.05%': {surprise_pct > 0.05} ✅")
    print(f"  Both conditions met: Should trigger sentiment")
    
    if their_result['usd_sentiment'] == 'neutral':
        print(f"❌ THEIR SYSTEM: Neutral despite conditions being met!")
        print(f"   This proves they DON'T evaluate conditions!")
    else:
        print(f"✅ THEIR SYSTEM: Applied sentiment as expected")

def test_zero_division():
    """Test zero division edge case."""
    print(f"\n=== ZERO DIVISION TEST ===")
    
    our_system = OurSystem('config/sentiment_rules.yaml')
    
    test_data = {
        'timestamp': '2024-01-04 13:14:00',
        'currency': 'USD',
        'event': 'CPI m/m',
        'importance': 'High',
        'actual': '0.1%',
        'forecast': '0.0%',  # Zero forecast!
        'previous': '0.2%'
    }
    
    row = pd.Series(test_data)
    
    try:
        result = our_system.classify_event(row)
        print(f"✅ Zero forecast handled: {result['reason']}")
    except Exception as e:
        print(f"❌ Zero division error: {e}")

def main():
    """Run condition evaluation tests."""
    test_condition_evaluation()
    test_zero_division()

if __name__ == "__main__":
    main()
