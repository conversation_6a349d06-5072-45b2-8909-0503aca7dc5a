#!/usr/bin/env python3
"""Advanced bug testing for both sentiment systems."""

import pandas as pd
import yaml
import sys
import os

# Add the scripts directory to the path
sys.path.append('scripts')

from sentiment_labeler import <PERSON>timent<PERSON><PERSON><PERSON>

def test_percentage_parsing_bug():
    """Test if percentage symbols in data cause issues."""
    print("=== PERCENTAGE PARSING BUG TEST ===")
    
    labeler = SentimentLabeler('config/sentiment_rules.yaml')
    
    # Test various percentage formats
    test_cases = [
        {'actual': '0.1%', 'forecast': '0.2%', 'expected': 'should work'},
        {'actual': '0.1', 'forecast': '0.2', 'expected': 'should work'},
        {'actual': '0.1%', 'forecast': '0.2', 'expected': 'mixed format'},
        {'actual': 'N/A', 'forecast': '0.2%', 'expected': 'missing actual'},
        {'actual': '0.1%', 'forecast': 'N/A', 'expected': 'missing forecast'},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\nTest {i+1}: {case['expected']}")
        test_data = {
            'timestamp': '2024-01-04 13:14:00',
            'currency': 'USD',
            'event': 'CPI m/m',
            'importance': 'High',
            'actual': case['actual'],
            'forecast': case['forecast'],
            'previous': '0.0%'
        }
        
        row = pd.Series(test_data)
        
        try:
            result = labeler.classify_event(row)
            print(f"  ✅ Parsed: actual={labeler._parse_numeric_value(case['actual'])}, forecast={labeler._parse_numeric_value(case['forecast'])}")
            print(f"  Result: {result['usd_sentiment']} (confidence: {result['confidence']})")
        except Exception as e:
            print(f"  ❌ Error: {e}")

def test_unemployment_inverse_logic():
    """Test unemployment rate inverse logic."""
    print(f"\n=== UNEMPLOYMENT INVERSE LOGIC TEST ===")
    
    labeler = SentimentLabeler('config/sentiment_rules.yaml')
    
    # Test unemployment rate (should be inverse)
    test_data = {
        'timestamp': '2024-01-04 13:14:00',
        'currency': 'USD',
        'event': 'Unemployment Rate',
        'importance': 'High',
        'actual': '4.0%',  # Lower unemployment
        'forecast': '4.2%',  # Expected higher
        'previous': '4.1%'
    }
    
    row = pd.Series(test_data)
    result = labeler.classify_event(row)
    
    print(f"Unemployment Rate Test:")
    print(f"  Actual: {row['actual']} | Forecast: {row['forecast']}")
    print(f"  Lower unemployment = Better economy = USD bullish")
    print(f"  Result: USD {result['usd_sentiment']}, Gold {result['gold_sentiment']}")
    print(f"  Reason: {result['reason']}")
    
    # Check if logic is correct
    if result['usd_sentiment'] == 'bullish' and result['gold_sentiment'] == 'bearish':
        print(f"  ✅ Unemployment inverse logic CORRECT")
    else:
        print(f"  ❌ Unemployment inverse logic WRONG")

def test_pmi_threshold_logic():
    """Test PMI 50 threshold logic."""
    print(f"\n=== PMI THRESHOLD LOGIC TEST ===")
    
    labeler = SentimentLabeler('config/sentiment_rules.yaml')
    
    test_cases = [
        {'actual': '52.1', 'forecast': '51.8', 'desc': 'Expansion stronger (>50, beat forecast)'},
        {'actual': '48.5', 'forecast': '49.2', 'desc': 'Contraction deeper (<50, missed forecast)'},
        {'actual': '51.2', 'forecast': '51.8', 'desc': 'Expansion but weaker (>50, missed forecast)'},
        {'actual': '49.1', 'forecast': '48.5', 'desc': 'Contraction but better (<50, beat forecast)'},
    ]
    
    for case in test_cases:
        test_data = {
            'timestamp': '2024-01-04 13:14:00',
            'currency': 'USD',
            'event': 'ISM Manufacturing PMI',
            'importance': 'High',
            'actual': case['actual'],
            'forecast': case['forecast'],
            'previous': '50.0'
        }
        
        row = pd.Series(test_data)
        result = labeler.classify_event(row)
        
        print(f"\n{case['desc']}:")
        print(f"  Actual: {case['actual']} | Forecast: {case['forecast']}")
        print(f"  USD: {result['usd_sentiment']} | Gold: {result['gold_sentiment']}")
        print(f"  Confidence: {result['confidence']}")
        print(f"  Reason: {result['reason']}")

def test_currency_correlation():
    """Test EUR/GBP currency correlation logic."""
    print(f"\n=== CURRENCY CORRELATION TEST ===")
    
    labeler = SentimentLabeler('config/sentiment_rules.yaml')
    
    # Test EUR event
    test_data = {
        'timestamp': '2024-01-04 13:14:00',
        'currency': 'EUR',
        'event': 'German Prelim CPI m/m',
        'importance': 'High',
        'actual': '0.3%',  # Higher inflation
        'forecast': '0.2%',
        'previous': '0.1%'
    }
    
    row = pd.Series(test_data)
    result = labeler.classify_event(row)
    
    print(f"EUR Event Test (German CPI):")
    print(f"  Higher inflation = EUR strength potential")
    print(f"  USD: {result['usd_sentiment']}")
    print(f"  EUR: {result['eur_sentiment']}")
    print(f"  GBP: {result['gbp_sentiment']}")
    print(f"  Gold: {result['gold_sentiment']}")
    
    # Check correlation logic
    if result['usd_sentiment'] == 'bullish' and result['eur_sentiment'] == 'bullish':
        print(f"  ✅ EUR correlation logic looks correct")
    else:
        print(f"  ⚠️  EUR correlation may need review")

def test_confidence_calculation():
    """Test confidence calculation edge cases."""
    print(f"\n=== CONFIDENCE CALCULATION TEST ===")
    
    labeler = SentimentLabeler('config/sentiment_rules.yaml')
    
    # Test high importance event
    test_data = {
        'timestamp': '2024-01-04 13:14:00',
        'currency': 'USD',
        'event': 'CPI m/m',  # Tier 1 event
        'importance': 'High',
        'actual': '0.5%',  # Large surprise
        'forecast': '0.2%',
        'previous': '0.1%'
    }
    
    row = pd.Series(test_data)
    result = labeler.classify_event(row)
    
    print(f"High Impact Event (CPI):")
    print(f"  Large surprise: {result['surprise_pct']:.1f}%")
    print(f"  Confidence: {result['confidence']}")
    print(f"  Should be high confidence (>0.8)")
    
    if result['confidence'] > 0.8:
        print(f"  ✅ High confidence as expected")
    else:
        print(f"  ⚠️  Confidence lower than expected")

def main():
    """Run advanced bug tests."""
    print("🔍 ADVANCED BUG TESTING")
    print("=" * 50)
    
    test_percentage_parsing_bug()
    test_unemployment_inverse_logic()
    test_pmi_threshold_logic()
    test_currency_correlation()
    test_confidence_calculation()
    
    print("\n" + "=" * 50)
    print("📋 ADVANCED TESTING COMPLETE")

if __name__ == "__main__":
    main()
