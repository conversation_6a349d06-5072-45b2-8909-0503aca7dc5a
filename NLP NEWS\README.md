# 📊 Backtestable High-Impact News Trading Strategy

A robust system for scraping economic news events and backtesting trading strategies based on fundamental analysis.

## 🚀 Project Status

### ✅ Phase 1: COMPLETED ✅
- [x] Enhanced project structure with robust folder organization
- [x] Comprehensive ForexFactory scraper with all robustness features
- [x] Configuration management with YAML files
- [x] Logging and error handling
- [x] Data validation and quality checks
- [x] Rate limiting and anti-detection measures
- [x] **Batch scraper for historical data collection**
- [x] **6 months of historical data collected (Jan-June 2024)**
- [x] **Data consolidation and validation system**
- [x] **Jupyter notebook for data analysis**

### 📊 Data Collection Results
- **✅ 452 high-impact economic events** collected and validated
- **✅ 8 major currencies** (USD, GBP, CAD, EUR, AUD, NZD, JPY, CHF)
- **✅ 26 weekly CSV files** successfully consolidated
- **✅ 77% data completeness** (events with actual values)
- **✅ Clean, production-ready dataset** for sentiment analysis

### ✅ Phase 2: COMPLETED ✅
- [x] **Build sentiment labeling system** (rule-based classification)
- [x] Create comprehensive YAML-based sentiment rules for economic indicators  
- [x] Implement advanced sentiment labeler script with confidence scoring
- [x] Validate sentiment output and create labeled dataset
- [x] **100% event coverage** (0 unknown events remaining)
- [x] **Hybrid framework** ready for future NLP integration

### 📊 Sentiment Analysis Results
- **✅ 452 events processed** with 100% coverage
- **✅ 342 quantitative events** classified using rule-based analysis
- **✅ 110 non-quantitative events** prepared for NLP integration
- **✅ Gold sentiment distribution**: 164 bullish, 119 bearish, 169 neutral signals
- **✅ Confidence scoring** based on data quality and event importance

### 🔄 Next Steps (Phase 3+)
- [ ] **Phase 3**: Implement price data loader (1-minute OHLCV data)
- [ ] **Phase 4**: Create backtesting engine with entry/exit logic
- [ ] **Phase 5**: Build analytics dashboard and performance metrics

## 📁 Project Structure

```
backtest-news-strategy/
├── data/
│   ├── news/
│   │   ├── raw/                # 26 weekly CSV files (Jan-June 2024)
│   │   ├── processed/          # Consolidated & cleaned datasets
│   │   └── cache/              # Scraping progress cache
│   └── prices/                 # OHLCV price data (future)
├── scripts/
│   ├── scraper.py              # Main ForexFactory scraper
│   ├── batch_scraper.py        # Historical data collection
│   ├── sentiment_labeler.py    # Advanced sentiment classification system
│   └── [future scripts]
├── config/
│   ├── settings.yaml           # General scraper settings
│   ├── sentiment_rules.yaml    # Comprehensive sentiment classification rules
│   └── [future configs]
├── logs/                       # Application logs
├── tests/                      # Unit tests
├── notebooks/
│   └── data_analysis.ipynb     # Data consolidation & validation
├── requirements.txt            # Python dependencies
├── prd.md                      # Product Requirements Document
└── README.md                   # This file
```

## 🛠️ Setup & Installation

### Prerequisites
- Python 3.8+
- Chrome browser (for Selenium)
- Virtual environment (recommended)
- Jupyter notebook (for data analysis)

### Installation Steps

1. **Clone/Setup Project**
   ```bash
   # Already done - you're in the project directory
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Jupyter (if not already installed)**
   ```bash
   pip install jupyter
   ```

4. **Verify Setup**
   ```bash
   python scripts/scraper.py --help
   ```

## 🔧 Configuration

### Main Settings (`config/settings.yaml`)
- **Rate limiting**: 2.5 seconds between requests
- **Retry logic**: 3 attempts with exponential backoff
- **User agent rotation**: Multiple browser agents to avoid detection
- **Data validation**: High-impact events only, major currencies
- **Logging**: Comprehensive file and console logging

### Sentiment Rules (`config/sentiment_rules.yaml`)
- **Quantitative events**: 6 categories (employment, inflation, growth, business activity, consumer, interest rates)
- **Non-quantitative events**: Framework for Fed communications, CB speeches, policy documents
- **Asset-specific sentiment mapping**: USD, EUR, GBP, Gold with inverse correlations
- **Confidence scoring**: Multi-factor system based on data quality and event importance
- **100% event coverage**: All 452 events properly classified

## 📊 Usage

### Historical Data Collection (Completed)
```bash
# Collect 6 months of data (already done)
python scripts/batch_scraper.py

# Monitor progress
python scripts/monitor_progress.py
```

### Data Analysis & Consolidation (Completed)
```bash
# Run Jupyter notebook for data analysis
jupyter notebook notebooks/data_analysis.ipynb

# Or view the consolidated dataset directly
# File: data/news/processed/consolidated_events_20250706_154235.csv
```

### Sentiment Analysis (Completed)
```bash
# Run sentiment labeler on consolidated dataset
python scripts/sentiment_labeler.py \
  --input "data/news/processed/consolidated_events_20250706_154235.csv" \
  --output "data/news/processed/labeled_events_complete.csv" \
  --report "data/news/processed/sentiment_report_complete.yaml"

# View results
# Labeled dataset: data/news/processed/labeled_events_complete.csv
# Analysis report: data/news/processed/sentiment_report_complete.yaml
```

### Basic Scraping (Single Week)
```bash
# Scrape current week
python scripts/scraper.py jan1.2024

# Scrape specific week with custom config
python scripts/scraper.py dec25.2023 --config config/settings.yaml
```

## 🧪 Features Implemented

### Robustness Features ✅
- **Rate Limiting**: 2-3 second delays with randomization
- **User Agent Rotation**: Multiple browser identities
- **Retry Logic**: Exponential backoff on failures
- **Timeout Handling**: 30-second request timeouts
- **Comprehensive Logging**: Debug, info, warning, error levels

### Data Quality ✅
- **Field Validation**: Required fields checking
- **Duplicate Removal**: Automatic deduplication
- **Currency Filtering**: Major currencies only
- **Impact Filtering**: High-impact events only
- **Timestamp Standardization**: UTC timezone consistency

### Anti-Detection ✅
- **Headless Browser**: Optional visible/headless mode
- **Random Delays**: Avoid pattern detection
- **Browser Fingerprint Masking**: Remove automation signatures
- **Request Spacing**: Human-like interaction timing

### Data Consolidation ✅
- **Batch Processing**: Automated historical data collection
- **Progress Tracking**: YAML-based progress monitoring
- **Data Validation**: Comprehensive quality checks
- **Jupyter Integration**: Interactive data analysis
- **Multiple Output Formats**: Main dataset + detailed tracking

### Sentiment Analysis ✅
- **Rule-Based Classification**: 342 quantitative events using actual vs forecast analysis
- **NLP Framework**: 110 non-quantitative events ready for contextual analysis
- **Confidence Scoring**: Multi-factor confidence based on data quality and event importance
- **Asset Correlation**: USD strength/weakness mapped to gold price movements
- **100% Coverage**: All 452 events properly categorized and analyzed

## 📈 Data Output

### Consolidated Dataset
- **Main File**: `data/news/processed/consolidated_events_20250706_154235.csv`
- **Size**: 452 events, 40KB
- **Coverage**: January 1 - June 30, 2024
- **Quality**: 77% events with actual values

### Sentiment-Labeled Dataset
- **Labeled File**: `data/news/processed/labeled_events_complete.csv`
- **Size**: 452 events with sentiment analysis, 99KB
- **Features**: USD/Gold sentiment, confidence scores, surprise percentages
- **Categories**: 10 event categories, 100% classification coverage
- **Analysis Report**: `data/news/processed/sentiment_report_complete.yaml`

### Consolidated CSV Format
```csv
timestamp,currency,event,impact,actual,forecast,previous,scraped_at
2024-01-05T08:30:00,USD,Non-Farm Payrolls,High,216K,170K,199K,2024-01-05T12:34:56
```

### Sentiment-Labeled CSV Format
```csv
timestamp,currency,event,impact,actual,forecast,previous,scraped_at,usd_sentiment,gold_sentiment,sentiment_confidence,sentiment_category,surprise_pct,surprise_direction
2024-01-05T19:15:00,USD,Non-Farm Employment Change,High,216K,168K,199K,2024-07-06T08:54:29,bullish,bearish,0.9,employment,28.57,positive
```

### Fields Explanation

**Base Fields:**
- **timestamp**: Event time in UTC ISO format
- **currency**: Currency affected (USD, EUR, etc.)
- **event**: Economic indicator name
- **impact**: Always "High" (filtered for high-impact only)
- **actual**: Actual reported value
- **forecast**: Market forecast/consensus
- **previous**: Previous period value
- **scraped_at**: When data was collected

**Sentiment Analysis Fields:**
- **usd_sentiment**: USD impact (bullish/bearish/neutral)
- **gold_sentiment**: Gold impact (bullish/bearish/neutral) 
- **sentiment_confidence**: Classification confidence (0.0-1.0)
- **sentiment_category**: Event category (employment, inflation, etc.)
- **surprise_pct**: Actual vs forecast surprise magnitude
- **surprise_direction**: Positive/negative surprise direction

## 🔍 Troubleshooting

### Common Issues

1. **Chrome Driver Issues**
   ```bash
   # Update webdriver-manager
   pip install --upgrade webdriver-manager
   ```

2. **Rate Limiting Blocks**
   - Increase delay in `config/settings.yaml`
   - Use different user agents
   - Check logs for specific error messages

3. **No Data Scraped**
   - Verify week format (e.g., "jan1.2024")
   - Check if it's a holiday/weekend
   - Review ForexFactory calendar manually

4. **Jupyter Notebook Issues**
   ```bash
   # Install/update Jupyter
   pip install --upgrade jupyter
   
   # Start notebook server
   jupyter notebook
   ```

## 📝 Logging

Logs are written to:
- **Console**: Real-time progress
- **File**: `logs/scraper.log` (detailed debugging)

Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL

## 🎯 Development Roadmap

1. **✅ Phase 1 Complete**: Data collection and consolidation (452 events, 6 months)
2. **✅ Phase 2 Complete**: Sentiment labeling system (100% coverage, hybrid framework)
3. **🔄 Phase 3 Next**: Price data integration (1-minute OHLCV data)
4. **📋 Phase 4 Planned**: Backtesting engine (entry/exit logic, performance metrics)
5. **🎯 Phase 5 Planned**: Analytics dashboard (visualization, optimization)
6. **🚀 Phase 6 Future**: NLP enhancement (Fed statement analysis, context understanding)

## 🤝 Contributing

1. Follow the existing code structure
2. Add comprehensive logging
3. Update configuration files as needed
4. Test thoroughly before committing

## 📄 License

This project is for educational and research purposes. 