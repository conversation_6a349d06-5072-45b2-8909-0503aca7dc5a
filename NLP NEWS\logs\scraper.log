2025-07-06 14:08:05,770 - ForexFactoryScraper - INFO - scrape_week:192 - Starting scrape for week: jun30.2025
2025-07-06 14:08:05,771 - ForexFactoryScraper - INFO - _setup_driver:121 - Setting up Chrome WebDriver...
2025-07-06 14:08:05,771 - ForexFactoryScraper - INFO - _setup_driver:141 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:08:09,134 - ForexFactoryScraper - INFO - _setup_driver:150 - Chrome WebDriver setup successful
2025-07-06 14:08:09,134 - ForexFactoryScraper - INFO - scrape_week:200 - Scraping URL: https://www.forexfactory.com/calendar?week=jun30.2025
2025-07-06 14:08:16,399 - ForexFactoryScraper - INFO - _scrape_page:264 - Extracted 0 events from page
2025-07-06 14:08:16,409 - ForexFactoryScraper - WARNING - _validate_data:366 - No events to validate
2025-07-06 14:08:16,411 - ForexFactoryScraper - INFO - scrape_week:208 - Successfully scraped 0 events for week jun30.2025
2025-07-06 14:08:18,531 - ForexFactoryScraper - INFO - scrape_week:217 - WebDriver closed
2025-07-06 14:08:30,692 - ForexFactoryScraper - INFO - scrape_week:192 - Starting scrape for week: jul1.2024
2025-07-06 14:08:30,692 - ForexFactoryScraper - INFO - _setup_driver:121 - Setting up Chrome WebDriver...
2025-07-06 14:08:30,693 - ForexFactoryScraper - INFO - _setup_driver:141 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:08:33,724 - ForexFactoryScraper - INFO - _setup_driver:150 - Chrome WebDriver setup successful
2025-07-06 14:08:33,724 - ForexFactoryScraper - INFO - scrape_week:200 - Scraping URL: https://www.forexfactory.com/calendar?week=jul1.2024
2025-07-06 14:08:39,873 - ForexFactoryScraper - INFO - _scrape_page:264 - Extracted 0 events from page
2025-07-06 14:08:39,873 - ForexFactoryScraper - WARNING - _validate_data:366 - No events to validate
2025-07-06 14:08:39,875 - ForexFactoryScraper - INFO - scrape_week:208 - Successfully scraped 0 events for week jul1.2024
2025-07-06 14:08:41,985 - ForexFactoryScraper - INFO - scrape_week:217 - WebDriver closed
2025-07-06 14:08:41,985 - ForexFactoryScraper - WARNING - save_data:391 - No data to save
2025-07-06 14:10:59,707 - ForexFactoryScraperFixed - INFO - scrape_week:164 - Starting scrape for week: jul1.2024
2025-07-06 14:10:59,707 - ForexFactoryScraperFixed - INFO - _setup_driver:110 - Setting up Chrome WebDriver...
2025-07-06 14:10:59,707 - ForexFactoryScraperFixed - INFO - _setup_driver:130 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:11:02,645 - ForexFactoryScraperFixed - INFO - _setup_driver:139 - Chrome WebDriver setup successful
2025-07-06 14:11:02,645 - ForexFactoryScraperFixed - INFO - scrape_week:172 - Scraping URL: https://www.forexfactory.com/calendar?week=jul1.2024
2025-07-06 14:11:13,562 - ForexFactoryScraperFixed - ERROR - _extract_calendar_data:236 - Failed to parse calendar JSON data: Expecting property name enclosed in double quotes: line 2 column 1 (char 2)
2025-07-06 14:11:13,562 - ForexFactoryScraperFixed - WARNING - _validate_data:297 - No events to validate
2025-07-06 14:11:13,564 - ForexFactoryScraperFixed - INFO - scrape_week:189 - Successfully scraped 0 events for week jul1.2024
2025-07-06 14:11:15,687 - ForexFactoryScraperFixed - INFO - scrape_week:198 - WebDriver closed
2025-07-06 14:11:15,687 - ForexFactoryScraperFixed - WARNING - save_data:322 - No data to save
2025-07-06 14:12:26,411 - ForexFactoryScraperFinal - INFO - scrape_week:162 - Starting scrape for week: jul1.2024
2025-07-06 14:12:26,411 - ForexFactoryScraperFinal - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:12:26,411 - ForexFactoryScraperFinal - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:12:29,522 - ForexFactoryScraperFinal - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:12:29,522 - ForexFactoryScraperFinal - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jul1.2024
2025-07-06 14:12:39,953 - ForexFactoryScraperFinal - INFO - _extract_calendar_data_js:249 - Extracted 107 events using JavaScript
2025-07-06 14:12:39,956 - ForexFactoryScraperFinal - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:12:39,957 - ForexFactoryScraperFinal - INFO - _validate_data:338 - Data validation complete: 18 valid events
2025-07-06 14:12:39,957 - ForexFactoryScraperFinal - INFO - scrape_week:187 - Successfully scraped 18 events for week jul1.2024
2025-07-06 14:12:42,088 - ForexFactoryScraperFinal - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:12:42,091 - ForexFactoryScraperFinal - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_jul1.2024_20250706_141242.csv
2025-07-06 14:16:33,942 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jul1.2024
2025-07-06 14:16:33,943 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:16:33,943 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:16:36,809 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:16:36,809 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jul1.2024
2025-07-06 14:16:47,333 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 107 events using JavaScript
2025-07-06 14:16:47,335 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:16:47,336 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 18 valid events
2025-07-06 14:16:47,336 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 18 events for week jul1.2024
2025-07-06 14:16:49,477 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:16:49,480 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_jul1.2024_20250706_141649.csv
2025-07-06 14:23:01,077 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jan1.2024
2025-07-06 14:23:01,077 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:23:01,077 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:23:04,324 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:23:04,324 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jan1.2024
2025-07-06 14:23:14,873 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 78 events using JavaScript
2025-07-06 14:23:14,876 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:23:14,877 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 12 valid events
2025-07-06 14:23:14,877 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 12 events for week jan1.2024
2025-07-06 14:23:18,524 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:23:21,529 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jan8.2024
2025-07-06 14:23:21,529 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:23:21,529 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:23:24,793 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:23:24,793 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jan8.2024
2025-07-06 14:23:35,774 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 73 events using JavaScript
2025-07-06 14:23:35,776 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:23:35,776 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 10 valid events
2025-07-06 14:23:35,776 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 10 events for week jan8.2024
2025-07-06 14:23:37,893 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:23:40,897 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jan15.2024
2025-07-06 14:23:40,898 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:23:40,898 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:23:44,498 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:23:44,499 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jan15.2024
2025-07-06 14:23:54,717 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 112 events using JavaScript
2025-07-06 14:23:54,718 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:23:54,719 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 14 valid events
2025-07-06 14:23:54,719 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 14 events for week jan15.2024
2025-07-06 14:23:56,835 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:23:59,840 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jan22.2024
2025-07-06 14:23:59,841 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:23:59,841 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:24:03,402 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:24:03,402 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jan22.2024
2025-07-06 14:24:13,775 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 68 events using JavaScript
2025-07-06 14:24:13,777 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:24:13,778 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 22 valid events
2025-07-06 14:24:13,778 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 22 events for week jan22.2024
2025-07-06 14:24:15,901 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:24:18,907 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jan29.2024
2025-07-06 14:24:18,907 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:24:18,907 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:24:21,872 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:24:21,873 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jan29.2024
2025-07-06 14:24:32,746 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 106 events using JavaScript
2025-07-06 14:24:32,747 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:24:32,748 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 24 valid events
2025-07-06 14:24:32,748 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 24 events for week jan29.2024
2025-07-06 14:24:34,875 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:24:37,880 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: feb5.2024
2025-07-06 14:24:37,880 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:24:37,881 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:24:41,011 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:24:41,012 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=feb5.2024
2025-07-06 14:24:52,064 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 84 events using JavaScript
2025-07-06 14:24:52,066 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:24:52,066 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 12 valid events
2025-07-06 14:24:52,066 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 12 events for week feb5.2024
2025-07-06 14:24:54,187 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:24:57,193 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: feb12.2024
2025-07-06 14:24:57,193 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:24:57,193 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:25:00,770 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:25:00,771 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=feb12.2024
2025-07-06 14:25:11,337 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 107 events using JavaScript
2025-07-06 14:25:11,338 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:25:11,339 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 21 valid events
2025-07-06 14:25:11,339 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 21 events for week feb12.2024
2025-07-06 14:25:13,454 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:25:16,460 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: feb19.2024
2025-07-06 14:25:16,460 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:25:16,460 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:25:19,669 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:25:19,670 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=feb19.2024
2025-07-06 14:25:30,616 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 83 events using JavaScript
2025-07-06 14:25:30,617 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:25:30,618 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 15 valid events
2025-07-06 14:25:30,618 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 15 events for week feb19.2024
2025-07-06 14:25:34,284 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:25:37,290 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: feb26.2024
2025-07-06 14:25:37,290 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:25:37,290 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:25:40,318 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:25:40,318 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=feb26.2024
2025-07-06 14:25:50,719 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 109 events using JavaScript
2025-07-06 14:25:50,720 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:25:50,721 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 13 valid events
2025-07-06 14:25:50,721 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 13 events for week feb26.2024
2025-07-06 14:25:52,841 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:25:55,846 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: mar4.2024
2025-07-06 14:25:55,846 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:25:55,846 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:25:59,374 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:25:59,375 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=mar4.2024
2025-07-06 14:26:10,502 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 99 events using JavaScript
2025-07-06 14:26:10,504 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:26:10,504 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 21 valid events
2025-07-06 14:26:10,504 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 21 events for week mar4.2024
2025-07-06 14:26:12,687 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:26:15,693 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: mar11.2024
2025-07-06 14:26:15,693 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:26:15,693 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:26:18,641 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:26:18,642 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=mar11.2024
2025-07-06 14:26:29,353 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 70 events using JavaScript
2025-07-06 14:26:29,355 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:26:29,356 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 14 valid events
2025-07-06 14:26:29,356 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 14 events for week mar11.2024
2025-07-06 14:26:31,483 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:26:34,489 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: mar18.2024
2025-07-06 14:26:34,489 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:26:34,489 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:26:38,002 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:26:38,002 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=mar18.2024
2025-07-06 14:26:48,572 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 115 events using JavaScript
2025-07-06 14:26:48,573 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:26:48,573 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 35 valid events
2025-07-06 14:26:48,574 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 35 events for week mar18.2024
2025-07-06 14:26:50,689 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:26:53,694 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: mar25.2024
2025-07-06 14:26:53,695 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:26:53,695 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:26:56,888 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:26:56,889 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=mar25.2024
2025-07-06 14:27:07,433 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 79 events using JavaScript
2025-07-06 14:27:07,435 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:27:07,435 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 10 valid events
2025-07-06 14:27:07,435 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 10 events for week mar25.2024
2025-07-06 14:27:09,562 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:27:12,576 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: apr1.2024
2025-07-06 14:27:12,576 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:27:12,576 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:27:15,821 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:27:15,821 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=apr1.2024
2025-07-06 14:27:26,882 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 116 events using JavaScript
2025-07-06 14:27:26,884 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:27:26,884 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 13 valid events
2025-07-06 14:27:26,884 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 13 events for week apr1.2024
2025-07-06 14:27:29,007 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:27:32,012 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: apr8.2024
2025-07-06 14:27:32,012 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:27:32,012 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:27:35,321 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:27:35,321 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=apr8.2024
2025-07-06 14:27:45,846 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 84 events using JavaScript
2025-07-06 14:27:45,848 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:27:45,848 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 19 valid events
2025-07-06 14:27:45,849 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 19 events for week apr8.2024
2025-07-06 14:27:47,969 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:27:50,975 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: apr15.2024
2025-07-06 14:27:50,975 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:27:50,975 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:27:54,392 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:27:54,392 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=apr15.2024
2025-07-06 14:28:05,198 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 110 events using JavaScript
2025-07-06 14:28:05,200 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:28:05,200 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 17 valid events
2025-07-06 14:28:05,200 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 17 events for week apr15.2024
2025-07-06 14:28:07,333 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:28:10,339 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: apr22.2024
2025-07-06 14:28:10,339 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:28:10,339 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:28:13,290 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:28:13,291 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=apr22.2024
2025-07-06 14:28:24,429 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 79 events using JavaScript
2025-07-06 14:28:24,431 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:28:24,432 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 20 valid events
2025-07-06 14:28:24,432 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 20 events for week apr22.2024
2025-07-06 14:28:26,580 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:28:29,585 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: apr29.2024
2025-07-06 14:28:29,585 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:28:29,585 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:28:32,809 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:28:32,810 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=apr29.2024
2025-07-06 14:28:43,565 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 107 events using JavaScript
2025-07-06 14:28:43,567 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:28:43,567 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 21 valid events
2025-07-06 14:28:43,567 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 21 events for week apr29.2024
2025-07-06 14:28:45,705 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:28:48,711 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: may6.2024
2025-07-06 14:28:48,711 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:28:48,711 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:28:51,667 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:28:51,667 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=may6.2024
2025-07-06 14:29:02,182 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 102 events using JavaScript
2025-07-06 14:29:02,184 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:29:02,184 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 15 valid events
2025-07-06 14:29:02,184 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 15 events for week may6.2024
2025-07-06 14:29:04,313 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:29:07,320 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: may13.2024
2025-07-06 14:29:07,320 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:29:07,320 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:29:10,632 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:29:10,632 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=may13.2024
2025-07-06 14:29:21,021 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 94 events using JavaScript
2025-07-06 14:29:21,023 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:29:21,023 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 15 valid events
2025-07-06 14:29:21,023 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 15 events for week may13.2024
2025-07-06 14:29:23,150 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:29:26,156 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: may20.2024
2025-07-06 14:29:26,156 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:29:26,156 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:29:29,116 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:29:29,117 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=may20.2024
2025-07-06 14:29:39,480 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 102 events using JavaScript
2025-07-06 14:29:39,481 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:29:39,482 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 20 valid events
2025-07-06 14:29:39,482 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 20 events for week may20.2024
2025-07-06 14:29:43,139 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:29:46,144 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: may27.2024
2025-07-06 14:29:46,145 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:29:46,145 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:29:49,337 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:29:49,338 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=may27.2024
2025-07-06 14:29:59,832 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 89 events using JavaScript
2025-07-06 14:29:59,833 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:29:59,833 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 11 valid events
2025-07-06 14:29:59,833 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 11 events for week may27.2024
2025-07-06 14:30:01,932 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:30:04,937 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jun3.2024
2025-07-06 14:30:04,937 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:30:04,937 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:30:07,854 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:30:07,854 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jun3.2024
2025-07-06 14:30:18,745 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 101 events using JavaScript
2025-07-06 14:30:18,746 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:30:18,747 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 20 valid events
2025-07-06 14:30:18,747 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 20 events for week jun3.2024
2025-07-06 14:30:22,411 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:30:25,417 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jun10.2024
2025-07-06 14:30:25,417 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:30:25,417 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:30:28,648 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:30:28,648 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jun10.2024
2025-07-06 14:30:38,917 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 87 events using JavaScript
2025-07-06 14:30:38,919 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:30:38,919 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 20 valid events
2025-07-06 14:30:38,919 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 20 events for week jun10.2024
2025-07-06 14:30:41,043 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:30:44,048 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jun17.2024
2025-07-06 14:30:44,048 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:30:44,049 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:30:47,516 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:30:47,517 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jun17.2024
2025-07-06 14:30:57,728 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 99 events using JavaScript
2025-07-06 14:30:57,729 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:30:57,729 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 24 valid events
2025-07-06 14:30:57,730 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 24 events for week jun17.2024
2025-07-06 14:30:59,859 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:31:02,864 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jun24.2024
2025-07-06 14:31:02,865 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:31:02,865 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:31:05,780 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:31:05,780 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jun24.2024
2025-07-06 14:31:16,450 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 88 events using JavaScript
2025-07-06 14:31:16,451 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:31:16,452 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 14 valid events
2025-07-06 14:31:16,452 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 14 events for week jun24.2024
2025-07-06 14:31:18,576 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:33:56,692 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jan1.2024
2025-07-06 14:33:56,692 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:33:56,692 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:33:59,954 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:33:59,954 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jan1.2024
2025-07-06 14:34:10,646 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 78 events using JavaScript
2025-07-06 14:34:10,648 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:34:10,650 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 12 valid events
2025-07-06 14:34:10,650 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 12 events for week jan1.2024
2025-07-06 14:34:12,773 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:34:12,787 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_jan1.2024_20250706_143412.csv
2025-07-06 14:34:28,876 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: mar18.2024
2025-07-06 14:34:28,876 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:34:28,877 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:34:32,432 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:34:32,432 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=mar18.2024
2025-07-06 14:34:42,934 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 115 events using JavaScript
2025-07-06 14:34:42,937 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:34:42,938 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 35 valid events
2025-07-06 14:34:42,938 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 35 events for week mar18.2024
2025-07-06 14:34:45,055 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:34:45,058 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_mar18.2024_20250706_143445.csv
2025-07-06 14:39:14,338 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jan1.2024
2025-07-06 14:39:14,338 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:39:14,338 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:39:17,789 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:39:17,789 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jan1.2024
2025-07-06 14:39:29,117 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 78 events using JavaScript
2025-07-06 14:39:29,119 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:39:29,121 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 12 valid events
2025-07-06 14:39:29,121 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 12 events for week jan1.2024
2025-07-06 14:39:31,324 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:39:31,327 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_jan1.2024_20250706_143931.csv
2025-07-06 14:39:41,817 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jan8.2024
2025-07-06 14:39:41,818 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:39:41,818 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:39:45,337 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:39:45,337 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jan8.2024
2025-07-06 14:39:54,896 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 73 events using JavaScript
2025-07-06 14:39:54,898 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:39:54,899 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 10 valid events
2025-07-06 14:39:54,900 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 10 events for week jan8.2024
2025-07-06 14:39:57,029 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:39:57,032 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_jan8.2024_20250706_143957.csv
2025-07-06 14:39:59,037 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jan15.2024
2025-07-06 14:39:59,037 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:39:59,037 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:40:02,245 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:40:02,245 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jan15.2024
2025-07-06 14:40:13,328 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 112 events using JavaScript
2025-07-06 14:40:13,329 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:40:13,330 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 14 valid events
2025-07-06 14:40:13,330 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 14 events for week jan15.2024
2025-07-06 14:40:15,459 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:40:15,461 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_jan15.2024_20250706_144015.csv
2025-07-06 14:40:46,936 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jan22.2024
2025-07-06 14:40:46,936 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:40:46,936 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:40:49,945 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:40:49,945 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jan22.2024
2025-07-06 14:41:00,693 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 68 events using JavaScript
2025-07-06 14:41:00,695 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:41:00,696 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 22 valid events
2025-07-06 14:41:00,696 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 22 events for week jan22.2024
2025-07-06 14:41:04,366 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:41:04,369 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_jan22.2024_20250706_144104.csv
2025-07-06 14:41:07,374 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jan29.2024
2025-07-06 14:41:07,374 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:41:07,374 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:41:10,934 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:41:10,935 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jan29.2024
2025-07-06 14:41:22,026 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 106 events using JavaScript
2025-07-06 14:41:22,027 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:41:22,027 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 24 valid events
2025-07-06 14:41:22,027 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 24 events for week jan29.2024
2025-07-06 14:41:24,157 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:41:24,158 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_jan29.2024_20250706_144124.csv
2025-07-06 14:41:27,164 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: feb5.2024
2025-07-06 14:41:27,164 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:41:27,164 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:41:30,822 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:41:30,822 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=feb5.2024
2025-07-06 14:41:41,518 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 84 events using JavaScript
2025-07-06 14:41:41,520 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:41:41,520 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 12 valid events
2025-07-06 14:41:41,520 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 12 events for week feb5.2024
2025-07-06 14:41:43,660 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:41:43,661 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_feb5.2024_20250706_144143.csv
2025-07-06 14:41:46,666 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: feb12.2024
2025-07-06 14:41:46,667 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:41:46,667 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:41:49,909 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:41:49,909 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=feb12.2024
2025-07-06 14:42:00,774 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 107 events using JavaScript
2025-07-06 14:42:00,776 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:42:00,776 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 21 valid events
2025-07-06 14:42:00,776 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 21 events for week feb12.2024
2025-07-06 14:42:02,892 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:42:02,893 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_feb12.2024_20250706_144202.csv
2025-07-06 14:42:05,899 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: feb19.2024
2025-07-06 14:42:05,900 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:42:05,900 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:42:09,068 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:42:09,068 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=feb19.2024
2025-07-06 14:42:19,908 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 83 events using JavaScript
2025-07-06 14:42:19,909 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:42:19,909 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 15 valid events
2025-07-06 14:42:19,909 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 15 events for week feb19.2024
2025-07-06 14:42:22,029 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:42:22,030 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_feb19.2024_20250706_144222.csv
2025-07-06 14:42:25,036 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: feb26.2024
2025-07-06 14:42:25,036 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:42:25,036 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:42:28,367 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:42:28,368 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=feb26.2024
2025-07-06 14:42:38,719 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 109 events using JavaScript
2025-07-06 14:42:38,720 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:42:38,720 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 13 valid events
2025-07-06 14:42:38,721 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 13 events for week feb26.2024
2025-07-06 14:42:40,838 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:42:40,840 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_feb26.2024_20250706_144240.csv
2025-07-06 14:42:43,845 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: mar4.2024
2025-07-06 14:42:43,846 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:42:43,846 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:42:47,106 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:42:47,106 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=mar4.2024
2025-07-06 14:42:58,131 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 99 events using JavaScript
2025-07-06 14:42:58,133 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:42:58,133 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 21 valid events
2025-07-06 14:42:58,133 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 21 events for week mar4.2024
2025-07-06 14:43:00,284 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:43:00,285 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_mar4.2024_20250706_144300.csv
2025-07-06 14:43:03,290 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: mar11.2024
2025-07-06 14:43:03,290 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:43:03,290 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:43:06,202 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:43:06,202 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=mar11.2024
2025-07-06 14:43:16,310 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 70 events using JavaScript
2025-07-06 14:43:16,313 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:43:16,313 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 14 valid events
2025-07-06 14:43:16,314 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 14 events for week mar11.2024
2025-07-06 14:43:19,951 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:43:19,953 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_mar11.2024_20250706_144319.csv
2025-07-06 14:43:22,958 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: mar18.2024
2025-07-06 14:43:22,958 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:43:22,959 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:43:26,053 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:43:26,053 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=mar18.2024
2025-07-06 14:43:36,574 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 115 events using JavaScript
2025-07-06 14:43:36,576 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:43:36,576 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 35 valid events
2025-07-06 14:43:36,576 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 35 events for week mar18.2024
2025-07-06 14:43:38,703 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:43:38,705 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_mar18.2024_20250706_144338.csv
2025-07-06 14:43:41,710 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: mar25.2024
2025-07-06 14:43:41,710 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:43:41,710 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:43:44,837 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:43:44,837 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=mar25.2024
2025-07-06 14:43:55,364 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 79 events using JavaScript
2025-07-06 14:43:55,366 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:43:55,366 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 10 valid events
2025-07-06 14:43:55,366 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 10 events for week mar25.2024
2025-07-06 14:43:57,474 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:43:57,476 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_mar25.2024_20250706_144357.csv
2025-07-06 14:44:00,481 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: apr1.2024
2025-07-06 14:44:00,481 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:44:00,482 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:44:03,323 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:44:03,323 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=apr1.2024
2025-07-06 14:44:13,731 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 116 events using JavaScript
2025-07-06 14:44:13,733 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:44:13,733 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 13 valid events
2025-07-06 14:44:13,733 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 13 events for week apr1.2024
2025-07-06 14:44:15,852 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:44:15,853 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_apr1.2024_20250706_144415.csv
2025-07-06 14:44:18,859 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: apr8.2024
2025-07-06 14:44:18,859 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:44:18,860 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:44:21,967 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:44:21,968 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=apr8.2024
2025-07-06 14:44:32,953 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 84 events using JavaScript
2025-07-06 14:44:32,955 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:44:32,955 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 19 valid events
2025-07-06 14:44:32,956 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 19 events for week apr8.2024
2025-07-06 14:44:36,610 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:44:36,611 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_apr8.2024_20250706_144436.csv
2025-07-06 14:44:39,617 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: apr15.2024
2025-07-06 14:44:39,617 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:44:39,617 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:44:43,010 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:44:43,010 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=apr15.2024
2025-07-06 14:44:54,109 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 110 events using JavaScript
2025-07-06 14:44:54,111 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:44:54,111 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 17 valid events
2025-07-06 14:44:54,111 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 17 events for week apr15.2024
2025-07-06 14:44:56,228 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:44:56,230 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_apr15.2024_20250706_144456.csv
2025-07-06 14:44:59,236 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: apr22.2024
2025-07-06 14:44:59,236 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:44:59,236 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:45:02,083 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:45:02,083 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=apr22.2024
2025-07-06 14:45:12,870 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 79 events using JavaScript
2025-07-06 14:45:12,871 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:45:12,872 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 20 valid events
2025-07-06 14:45:12,872 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 20 events for week apr22.2024
2025-07-06 14:45:14,998 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:45:15,000 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_apr22.2024_20250706_144514.csv
2025-07-06 14:45:18,005 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: apr29.2024
2025-07-06 14:45:18,005 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:45:18,005 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:45:21,191 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:45:21,191 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=apr29.2024
2025-07-06 14:45:33,182 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 107 events using JavaScript
2025-07-06 14:45:33,183 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:45:33,184 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 21 valid events
2025-07-06 14:45:33,184 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 21 events for week apr29.2024
2025-07-06 14:45:35,301 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:45:35,302 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_apr29.2024_20250706_144535.csv
2025-07-06 14:45:38,308 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: may6.2024
2025-07-06 14:45:38,308 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:45:38,308 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:45:41,179 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:45:41,179 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=may6.2024
2025-07-06 14:45:52,661 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 102 events using JavaScript
2025-07-06 14:45:52,662 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:45:52,663 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 15 valid events
2025-07-06 14:45:52,663 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 15 events for week may6.2024
2025-07-06 14:45:54,778 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:45:54,779 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_may6.2024_20250706_144554.csv
2025-07-06 14:45:57,785 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: may13.2024
2025-07-06 14:45:57,785 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:45:57,785 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:46:01,153 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:46:01,154 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=may13.2024
2025-07-06 14:46:13,411 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 94 events using JavaScript
2025-07-06 14:46:13,413 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:46:13,413 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 15 valid events
2025-07-06 14:46:13,413 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 15 events for week may13.2024
2025-07-06 14:46:15,536 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:46:15,538 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_may13.2024_20250706_144615.csv
2025-07-06 14:46:18,544 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: may20.2024
2025-07-06 14:46:18,544 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:46:18,544 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:46:21,678 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:46:21,678 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=may20.2024
2025-07-06 14:46:33,055 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 102 events using JavaScript
2025-07-06 14:46:33,057 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:46:33,058 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 20 valid events
2025-07-06 14:46:33,058 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 20 events for week may20.2024
2025-07-06 14:46:35,181 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:46:35,183 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_may20.2024_20250706_144635.csv
2025-07-06 14:46:38,189 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: may27.2024
2025-07-06 14:46:38,189 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:46:38,189 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:46:41,349 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:46:41,349 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=may27.2024
2025-07-06 14:46:52,247 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 89 events using JavaScript
2025-07-06 14:46:52,248 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:46:52,248 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 11 valid events
2025-07-06 14:46:52,249 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 11 events for week may27.2024
2025-07-06 14:46:54,366 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:46:54,367 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_may27.2024_20250706_144654.csv
2025-07-06 14:46:57,373 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jun3.2024
2025-07-06 14:46:57,373 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:46:57,373 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:47:00,530 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:47:00,531 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jun3.2024
2025-07-06 14:47:12,010 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 101 events using JavaScript
2025-07-06 14:47:12,011 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:47:12,011 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 20 valid events
2025-07-06 14:47:12,011 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 20 events for week jun3.2024
2025-07-06 14:47:14,118 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:47:14,119 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_jun3.2024_20250706_144714.csv
2025-07-06 14:47:17,125 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jun10.2024
2025-07-06 14:47:17,125 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:47:17,125 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
2025-07-06 14:47:19,998 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:47:19,998 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jun10.2024
2025-07-06 14:47:30,931 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 87 events using JavaScript
2025-07-06 14:47:30,932 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:47:30,932 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 20 valid events
2025-07-06 14:47:30,932 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 20 events for week jun10.2024
2025-07-06 14:47:33,035 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:47:33,037 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_jun10.2024_20250706_144733.csv
2025-07-06 14:47:36,043 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jun17.2024
2025-07-06 14:47:36,043 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:47:36,043 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0
2025-07-06 14:47:39,485 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:47:39,485 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jun17.2024
2025-07-06 14:47:50,169 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 99 events using JavaScript
2025-07-06 14:47:50,171 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:47:50,171 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 24 valid events
2025-07-06 14:47:50,171 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 24 events for week jun17.2024
2025-07-06 14:47:52,273 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:47:52,274 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_jun17.2024_20250706_144752.csv
2025-07-06 14:47:55,280 - ForexFactoryScraper - INFO - scrape_week:162 - Starting scrape for week: jun24.2024
2025-07-06 14:47:55,280 - ForexFactoryScraper - INFO - _setup_driver:108 - Setting up Chrome WebDriver...
2025-07-06 14:47:55,280 - ForexFactoryScraper - INFO - _setup_driver:128 - Using user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15
2025-07-06 14:47:58,164 - ForexFactoryScraper - INFO - _setup_driver:137 - Chrome WebDriver setup successful
2025-07-06 14:47:58,164 - ForexFactoryScraper - INFO - scrape_week:170 - Scraping URL: https://www.forexfactory.com/calendar?week=jun24.2024
2025-07-06 14:48:08,661 - ForexFactoryScraper - INFO - _extract_calendar_data_js:249 - Extracted 88 events using JavaScript
2025-07-06 14:48:08,663 - ForexFactoryScraper - INFO - _validate_data:327 - Removed 0 duplicate events
2025-07-06 14:48:08,664 - ForexFactoryScraper - INFO - _validate_data:338 - Data validation complete: 14 valid events
2025-07-06 14:48:08,664 - ForexFactoryScraper - INFO - scrape_week:187 - Successfully scraped 14 events for week jun24.2024
2025-07-06 14:48:12,348 - ForexFactoryScraper - INFO - scrape_week:196 - WebDriver closed
2025-07-06 14:48:12,350 - ForexFactoryScraper - INFO - save_data:358 - Data saved to: data/news/raw\forexfactory_jun24.2024_20250706_144812.csv
