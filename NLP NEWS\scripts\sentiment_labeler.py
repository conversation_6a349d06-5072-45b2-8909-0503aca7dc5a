#!/usr/bin/env python3
"""
Economic Event Sentiment Labeler
Phase 2: News Sentiment Analysis System

This script classifies economic events as bullish/bearish/neutral for different assets
using rule-based analysis for quantitative events and preparing framework for NLP.
"""

import pandas as pd
import numpy as np
import yaml
import logging
import re
from typing import Dict, List, Tuple, Optional, Any, Union
from pathlib import Path
from datetime import datetime

class SentimentLabeler:
    """
    Main sentiment labeling engine for economic events.
    
    Handles:
    - Quantitative events using actual vs forecast deltas
    - Non-quantitative events (framework for future NLP)
    - Confidence scoring based on data quality and event importance
    """
    
    def __init__(self, config_path: str = "config/sentiment_rules.yaml"):
        """Initialize the sentiment labeler with configuration."""
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self._setup_logging()
        
        # Cache for event mappings
        self._quantitative_events = self._build_quantitative_mapping()
        self._non_quantitative_events = self._build_non_quantitative_mapping()
        
        logging.info("SentimentLabe<PERSON> initialized successfully")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load sentiment classification rules from YAML config."""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            raise FileNotFoundError(f"Config file not found: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in config file: {e}")
    
    def _setup_logging(self):
        """Setup logging for sentiment analysis."""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'sentiment_labeler.log'),
                logging.StreamHandler()
            ]
        )
    
    def _build_quantitative_mapping(self) -> Dict[str, str]:
        """Build mapping of quantitative events to their categories."""
        mapping = {}
        for category, data in self.config['quantitative_events'].items():
            for event in data['events']:
                mapping[event] = category
        return mapping
    
    def _build_non_quantitative_mapping(self) -> Dict[str, str]:
        """Build mapping of non-quantitative events to their categories."""
        mapping = {}
        for category, data in self.config['non_quantitative_events'].items():
            for event in data['events']:
                mapping[event] = category
        return mapping
    
    def _parse_numeric_value(self, value: Any) -> Optional[float]:
        """Parse numeric value from string, handling various formats."""
        if pd.isna(value) or value == '' or value is None:
            return None
            
        # Handle string conversion
        value_str = str(value).strip()
        
        # Remove common suffixes/prefixes
        value_str = re.sub(r'[%KMB]$', '', value_str)
        value_str = value_str.replace(',', '')
        
        # Handle special cases
        if value_str in ['', '-', 'N/A', 'n/a']:
            return None
        
        try:
            # Handle ranges (e.g., "4.17|2.5")
            if '|' in value_str:
                value_str = value_str.split('|')[0]
            
            # Handle comparison operators
            if value_str.startswith('<'):
                value_str = value_str[1:]
            elif value_str.startswith('>'):
                value_str = value_str[1:]
                
            return float(value_str)
        except (ValueError, TypeError):
            logging.warning(f"Could not parse numeric value: {value}")
            return None
    
    def _calculate_surprise(self, actual: Optional[float], forecast: Optional[float],
                          event_name: str) -> Tuple[float, str]:
        """
        Calculate surprise magnitude and direction.

        Returns:
            Tuple of (surprise_percentage, direction)
        """
        if actual is None or forecast is None:
            return 0.0, "neutral"

        if forecast == 0:
            return 0.0, "neutral"

        surprise_pct = ((actual - forecast) / abs(forecast)) * 100

        # Standard direction calculation (actual vs forecast)
        direction = "positive" if surprise_pct > 0 else "negative"

        return abs(surprise_pct), direction

    def _is_inverse_indicator(self, event_name: str) -> bool:
        """
        Check if an economic indicator has inverse relationship
        (lower values = better for economy).
        """
        inverse_indicators = [
            'unemployment rate', 'unemployment claims', 'claimant count'
        ]
        event_lower = event_name.lower()
        return any(indicator in event_lower for indicator in inverse_indicators)

    def _get_inverse_reason(self, event_name: str, forecast: float, actual: float, direction: str) -> str:
        """Generate appropriate reason text for inverse indicators."""
        if "unemployment rate" in event_name.lower():
            if direction == "better":
                return f"Lower unemployment than expected: {forecast}% → {actual}%"
            else:
                return f"Higher unemployment than expected: {forecast}% → {actual}%"
        elif "unemployment claims" in event_name.lower() or "claimant count" in event_name.lower():
            if direction == "better":
                return f"Fewer claims than expected: {forecast} → {actual}"
            else:
                return f"More claims than expected: {forecast} → {actual}"
        elif any(term in event_name.lower() for term in ['cpi', 'ppi', 'inflation']):
            if direction == "better":
                return f"Lower inflation than expected: {forecast} → {actual}"
            else:
                return f"Higher inflation than expected: {forecast} → {actual}"
        else:
            if direction == "better":
                return f"Better than expected: {forecast} → {actual}"
            else:
                return f"Worse than expected: {forecast} → {actual}"
    
    def _calculate_confidence_score(self, row: pd.Series, 
                                  base_confidence: float) -> float:
        """Calculate confidence score based on data quality and event importance."""
        confidence = base_confidence
        
        # Data quality factor - extract scalar values and use proper boolean evaluation
        actual_val = row['actual']
        forecast_val = row['forecast']
        
        has_actual = bool(pd.notna(actual_val) and str(actual_val) != '')
        has_forecast = bool(pd.notna(forecast_val) and str(forecast_val) != '')
        
        if has_actual and has_forecast:
            confidence *= self.config['confidence_factors']['data_quality']['both_actual_forecast']
        elif has_actual:
            confidence *= self.config['confidence_factors']['data_quality']['actual_only']
        elif has_forecast:
            confidence *= self.config['confidence_factors']['data_quality']['forecast_only']
        else:
            confidence *= self.config['confidence_factors']['data_quality']['neither']
        
        # Event importance factor (simplified tier system)
        event_name = str(row['event']).lower()
        if any(term in event_name for term in ['nfp', 'non-farm', 'cpi', 'fed rate', 'fomc statement']):
            confidence *= self.config['confidence_factors']['event_importance']['tier_1']
        elif any(term in event_name for term in ['pmi', 'gdp', 'retail sales']):
            confidence *= self.config['confidence_factors']['event_importance']['tier_2']
        else:
            confidence *= self.config['confidence_factors']['event_importance']['tier_3']
        
        return min(confidence, 1.0)
    
    def _classify_quantitative_event(self, row: pd.Series) -> Dict[str, Any]:
        """Classify sentiment for quantitative economic events."""
        # Extract scalar values from pandas Series
        event_name = str(row['event'])
        category = self._quantitative_events.get(event_name)
        
        if not category:
            return self._create_neutral_result("Event not in quantitative mapping")
        
        # Parse actual and forecast values
        actual = self._parse_numeric_value(row['actual'])
        forecast = self._parse_numeric_value(row['forecast'])
        previous = self._parse_numeric_value(row['previous'])
        
        # Get category rules
        category_config = self.config['quantitative_events'][category]
        rules = category_config['sentiment_rules']
        
        # Calculate surprise
        surprise_pct, surprise_direction = self._calculate_surprise(actual, forecast, event_name)
        
        # Apply rules based on category
        sentiment_result = self._apply_category_rules(
            category, rules, actual, forecast, previous, 
            surprise_pct, surprise_direction, event_name
        )
        
        # Apply asset correlation validation and EUR/GBP sentiment mapping
        currency = str(row.get('currency', 'USD'))
        sentiment_result = self._apply_asset_correlation_validation(sentiment_result, currency)

        # Calculate confidence
        base_confidence = sentiment_result.get('confidence', 0.5)
        confidence = self._calculate_confidence_score(row, base_confidence)

        sentiment_result['confidence'] = confidence
        sentiment_result['surprise_pct'] = surprise_pct
        sentiment_result['surprise_direction'] = surprise_direction
        sentiment_result['category'] = category
        sentiment_result['method'] = 'rule_based_quantitative'

        return sentiment_result

    def _apply_asset_correlation_validation(self, sentiment_result: Dict[str, Any], currency: str) -> Dict[str, Any]:
        """Apply asset correlation validation and enhance EUR/GBP sentiment mapping."""

        # Get currency sentiment rules from config
        currency_rules = self.config.get('currency_sentiment_rules', {})

        if currency in currency_rules:
            currency_rule = currency_rules[currency]
            usd_sentiment = sentiment_result.get('usd_sentiment', 'neutral')

            # Apply currency-specific sentiment mapping
            if usd_sentiment == 'bullish' and currency == 'USD':
                # USD bullish events
                sentiment_result['eur_sentiment'] = 'bearish'  # USD strength = EUR weakness
                sentiment_result['gbp_sentiment'] = 'bearish'  # USD strength = GBP weakness

            elif usd_sentiment == 'bearish' and currency == 'USD':
                # USD bearish events
                sentiment_result['eur_sentiment'] = 'bullish'  # USD weakness = EUR strength
                sentiment_result['gbp_sentiment'] = 'bullish'  # USD weakness = GBP strength

            elif currency == 'EUR':
                # EUR-specific events
                if usd_sentiment == 'bullish':  # EUR strength
                    sentiment_result['eur_sentiment'] = 'bullish'
                    sentiment_result['gbp_sentiment'] = 'neutral'  # EUR vs GBP is more complex
                elif usd_sentiment == 'bearish':  # EUR weakness
                    sentiment_result['eur_sentiment'] = 'bearish'
                    sentiment_result['gbp_sentiment'] = 'neutral'

            elif currency == 'GBP':
                # GBP-specific events
                if usd_sentiment == 'bullish':  # GBP strength
                    sentiment_result['gbp_sentiment'] = 'bullish'
                    sentiment_result['eur_sentiment'] = 'neutral'  # GBP vs EUR is complex
                elif usd_sentiment == 'bearish':  # GBP weakness
                    sentiment_result['gbp_sentiment'] = 'bearish'
                    sentiment_result['eur_sentiment'] = 'neutral'

        # Validate USD-Gold inverse correlation
        usd_sentiment = sentiment_result.get('usd_sentiment', 'neutral')
        gold_sentiment = sentiment_result.get('gold_sentiment', 'neutral')

        if usd_sentiment == 'bullish' and gold_sentiment != 'bearish':
            sentiment_result['gold_sentiment'] = 'bearish'
            sentiment_result['reason'] += " (USD-Gold inverse correlation applied)"
        elif usd_sentiment == 'bearish' and gold_sentiment != 'bullish':
            sentiment_result['gold_sentiment'] = 'bullish'
            sentiment_result['reason'] += " (USD-Gold inverse correlation applied)"

        return sentiment_result

    def _get_category_threshold(self, category: str) -> float:
        """Get the surprise threshold for a specific category."""
        category_thresholds = self.config.get('processing_settings', {}).get('category_thresholds', {})
        return category_thresholds.get(category, self.config['processing_settings']['quantitative_threshold'])

    def _calculate_confidence_score(self, row: pd.Series, base_confidence: float) -> float:
        """Calculate confidence score based on data quality and event importance."""
        confidence = base_confidence

        # Data quality factor - extract scalar values and use proper boolean evaluation
        actual_val = row['actual']
        forecast_val = row['forecast']

        has_actual = bool(pd.notna(actual_val) and str(actual_val) != '')
        has_forecast = bool(pd.notna(forecast_val) and str(forecast_val) != '')

        if has_actual and has_forecast:
            confidence *= self.config['confidence_factors']['data_quality']['both_actual_forecast']
        elif has_actual:
            confidence *= self.config['confidence_factors']['data_quality']['actual_only']
        elif has_forecast:
            confidence *= self.config['confidence_factors']['data_quality']['forecast_only']
        else:
            confidence *= self.config['confidence_factors']['data_quality']['neither']

        # Event importance factor (simplified tier system)
        event_name = str(row['event']).lower()
        if any(term in event_name for term in ['nfp', 'non-farm', 'cpi', 'fed rate', 'fomc statement']):
            confidence *= self.config['confidence_factors']['event_importance']['tier_1']
        elif any(term in event_name for term in ['pmi', 'gdp', 'retail sales']):
            confidence *= self.config['confidence_factors']['event_importance']['tier_2']
        else:
            confidence *= self.config['confidence_factors']['event_importance']['tier_3']

        # Round to eliminate floating-point precision artifacts
        return round(min(confidence, 1.0), 3)

    def _apply_category_rules(self, category: str, rules: Dict, actual: Optional[float],
                            forecast: Optional[float], previous: Optional[float], surprise_pct: float,
                            surprise_direction: str, event_name: str) -> Dict[str, Any]:
        """Apply specific category rules to determine sentiment."""
        
        # Default neutral result
        result = {
            'usd_sentiment': 'neutral',
            'gold_sentiment': 'neutral',
            'eur_sentiment': 'neutral',
            'gbp_sentiment': 'neutral',
            'confidence': 0.3,
            'reason': 'No applicable rules'
        }
        
        # Handle missing data
        if actual is None:
            if forecast is None:
                return result
            # Use forecast-only logic (lower confidence)
            result['confidence'] = 0.2
            result['reason'] = 'Forecast-only data'
            return result
        
        # Interest rates - special handling (actual vs previous for rate changes)
        if category == 'interest_rates':
            if previous is not None and actual != previous:
                if actual > previous:
                    result.update(rules['rate_hike'])
                    result['reason'] = f"Rate hike: {previous}% → {actual}%"
                elif actual < previous:
                    result.update(rules['rate_cut'])
                    result['reason'] = f"Rate cut: {previous}% → {actual}%"
            elif forecast is not None and actual == forecast and actual == previous:
                # Rate held as expected
                result['reason'] = f"Rate held as expected at {actual}%"
                result['confidence'] = 0.3
            elif forecast is not None and actual != forecast:
                # Rate surprise vs forecast
                if actual > forecast:
                    result.update(rules.get('rate_hike', rules.get('stronger_than_expected', {})))
                    result['reason'] = f"Rate higher than expected: {forecast}% → {actual}%"
                elif actual < forecast:
                    result.update(rules.get('rate_cut', rules.get('weaker_than_expected', {})))
                    result['reason'] = f"Rate lower than expected: {forecast}% → {actual}%"
        
        # Standard actual vs forecast logic
        elif forecast is not None:
            # Use ultra-aggressive threshold system for maximum signal capture
            threshold = self._get_category_threshold(category)

            # STRICT THRESHOLD ENFORCEMENT: Only proceed if surprise exceeds threshold
            if surprise_pct <= threshold:
                result['reason'] = f"Surprise {surprise_pct:.2f}% below threshold {threshold}%"
                result['confidence'] = 0.1  # Very low confidence for sub-threshold events
                return result

            # Special cases for inverse indicators (lower = better for economy)
            if self._is_inverse_indicator(event_name):
                if actual < forecast and surprise_pct > threshold:
                    # Lower than expected = better for economy = USD bullish, Gold bearish
                    if "unemployment rate" in event_name.lower() and 'unemployment_rate_lower' in rules:
                        result.update(rules['unemployment_rate_lower'])
                    elif 'stronger_than_expected' in rules:
                        result.update(rules['stronger_than_expected'])
                    result['reason'] = self._get_inverse_reason(event_name, forecast, actual, "better")
                elif actual > forecast and surprise_pct > threshold:
                    # Higher than expected = worse for economy = USD bearish, Gold bullish
                    if "unemployment rate" in event_name.lower() and 'unemployment_rate_higher' in rules:
                        result.update(rules['unemployment_rate_higher'])
                    elif 'weaker_than_expected' in rules:
                        result.update(rules['weaker_than_expected'])
                    result['reason'] = self._get_inverse_reason(event_name, forecast, actual, "worse")

            # PMI special logic (50 = expansion/contraction threshold)
            elif "pmi" in event_name.lower() and actual is not None:
                if actual > forecast and surprise_pct > threshold:
                    if actual > 50 and 'expansion_stronger' in rules:
                        result.update(rules['expansion_stronger'])
                        result['reason'] = f"PMI expansion stronger than expected: {forecast} → {actual}"
                    elif 'stronger_than_expected' in rules:
                        result.update(rules['stronger_than_expected'])
                        result['reason'] = f"Better than expected: {forecast} → {actual}"
                elif actual < forecast and surprise_pct > threshold:
                    if actual < 50 and 'contraction_deeper' in rules:
                        result.update(rules['contraction_deeper'])
                        result['reason'] = f"PMI contraction deeper than expected: {forecast} → {actual}"
                    elif 'weaker_than_expected' in rules:
                        result.update(rules['weaker_than_expected'])
                        result['reason'] = f"Worse than expected: {forecast} → {actual}"

            elif surprise_direction == 'positive' and surprise_pct > threshold:
                # Standard positive surprise (actual > forecast)
                if 'stronger_than_expected' in rules:
                    result.update(rules['stronger_than_expected'])
                elif 'hotter_than_expected' in rules:
                    result.update(rules['hotter_than_expected'])
                elif 'stronger_growth' in rules:
                    result.update(rules['stronger_growth'])
                elif 'expansion_stronger' in rules:
                    result.update(rules['expansion_stronger'])
                elif 'stronger_confidence' in rules:
                    result.update(rules['stronger_confidence'])

                result['reason'] = f"Better than expected: {forecast} → {actual}"

            elif surprise_direction == 'negative' and surprise_pct > threshold:
                # Standard negative surprise (actual < forecast)
                if 'weaker_than_expected' in rules:
                    result.update(rules['weaker_than_expected'])
                elif 'cooler_than_expected' in rules:
                    result.update(rules['cooler_than_expected'])
                elif 'weaker_growth' in rules:
                    result.update(rules['weaker_growth'])
                elif 'contraction_deeper' in rules:
                    result.update(rules['contraction_deeper'])
                elif 'weaker_confidence' in rules:
                    result.update(rules['weaker_confidence'])

                result['reason'] = f"Worse than expected: {forecast} → {actual}"
        
        return result
    
    def _classify_non_quantitative_event(self, row: pd.Series) -> Dict[str, Any]:
        """Framework for classifying non-quantitative events (future NLP integration)."""
        event_name = str(row['event'])
        category = self._non_quantitative_events.get(event_name)
        
        if not category:
            return self._create_neutral_result("Event not in non-quantitative mapping")
        
        # Get category configuration
        category_config = self.config['non_quantitative_events'][category]
        
        result = {
            'usd_sentiment': 'neutral',
            'gold_sentiment': 'neutral',
            'eur_sentiment': 'neutral',
            'gbp_sentiment': 'neutral',
            'confidence': category_config.get('confidence', 0.3),
            'category': category,
            'method': 'non_quantitative_framework',
            'processing_required': category_config['processing_method'],
            'reason': f"Non-quantitative event requiring {category_config['processing_method']}"
        }
        
        # Placeholder for future NLP integration
        if category_config['processing_method'] == 'nlp_required':
            result['nlp_keywords'] = category_config.get('keywords', {})
            result['sentiment_approach'] = category_config['sentiment_approach']
        
        return result
    
    def _create_neutral_result(self, reason: str = "Default neutral") -> Dict[str, Any]:
        """Create neutral sentiment result."""
        return {
            'usd_sentiment': 'neutral',
            'gold_sentiment': 'neutral',
            'eur_sentiment': 'neutral',
            'gbp_sentiment': 'neutral',
            'confidence': 0.1,
            'category': 'unknown',
            'method': 'default_neutral',
            'reason': reason
        }
    
    def classify_event(self, row: pd.Series) -> Dict[str, Any]:
        """Classify a single economic event."""
        event_name = str(row['event'])
        
        # Determine if quantitative or non-quantitative
        if event_name in self._quantitative_events:
            return self._classify_quantitative_event(row)
        elif event_name in self._non_quantitative_events:
            return self._classify_non_quantitative_event(row)
        else:
            logging.warning(f"Unknown event type: {event_name}")
            return self._create_neutral_result(f"Unknown event: {event_name}")
    
    def process_dataset(self, input_file: str, output_file: Optional[str] = None) -> pd.DataFrame:
        """
        Process entire dataset and add sentiment labels.
        
        Args:
            input_file: Path to consolidated events CSV
            output_file: Optional output path for labeled dataset
            
        Returns:
            DataFrame with sentiment labels added
        """
        logging.info(f"Processing dataset: {input_file}")
        
        # Load data
        df = pd.read_csv(input_file)
        original_count = len(df)
        
        # Initialize result columns
        sentiment_columns = [
            'usd_sentiment', 'gold_sentiment', 'eur_sentiment', 'gbp_sentiment',
            'sentiment_confidence', 'sentiment_category', 'sentiment_method',
            'sentiment_reason', 'surprise_pct', 'surprise_direction'
        ]
        
        for col in sentiment_columns:
            df[col] = None
        
        # Process each event
        processed_count = 0
        quantitative_count = 0
        non_quantitative_count = 0
        
        for idx, row in df.iterrows():
            try:
                result = self.classify_event(row)
                
                # Map results to DataFrame columns
                df.loc[idx, 'usd_sentiment'] = result['usd_sentiment']
                df.loc[idx, 'gold_sentiment'] = result['gold_sentiment']
                df.loc[idx, 'eur_sentiment'] = result.get('eur_sentiment', 'neutral')
                df.loc[idx, 'gbp_sentiment'] = result.get('gbp_sentiment', 'neutral')
                df.loc[idx, 'sentiment_confidence'] = result['confidence']
                df.loc[idx, 'sentiment_category'] = result.get('category', 'unknown')
                df.loc[idx, 'sentiment_method'] = result.get('method', 'unknown')
                df.loc[idx, 'sentiment_reason'] = result.get('reason', 'No reason provided')
                df.loc[idx, 'surprise_pct'] = result.get('surprise_pct', 0.0)
                df.loc[idx, 'surprise_direction'] = result.get('surprise_direction', 'neutral')
                
                # Count processing types
                if result.get('method') == 'rule_based_quantitative':
                    quantitative_count += 1
                elif 'non_quantitative' in result.get('method', ''):
                    non_quantitative_count += 1
                
                processed_count += 1
                
            except Exception as e:
                logging.error(f"Error processing row {idx}: {e}")
                # Set default neutral values
                df.loc[idx, 'usd_sentiment'] = 'neutral'
                df.loc[idx, 'gold_sentiment'] = 'neutral'
                df.loc[idx, 'sentiment_confidence'] = 0.1
                df.loc[idx, 'sentiment_reason'] = f"Processing error: {str(e)}"
        
        # Generate processing summary
        logging.info(f"Dataset processing complete:")
        logging.info(f"  Total events: {original_count}")
        logging.info(f"  Successfully processed: {processed_count}")
        logging.info(f"  Quantitative events: {quantitative_count}")
        logging.info(f"  Non-quantitative events: {non_quantitative_count}")
        
        # Save results if output file specified
        if output_file:
            df.to_csv(output_file, index=False)
            logging.info(f"Labeled dataset saved to: {output_file}")
        
        return df
    
    def generate_summary_report(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Generate summary report of sentiment labeling results."""
        
        report = {
            'total_events': len(df),
            'processing_summary': {},
            'sentiment_distribution': {},
            'confidence_stats': {},
            'category_breakdown': {}
        }
        
        # Processing method breakdown
        method_counts = df['sentiment_method'].value_counts()
        report['processing_summary'] = method_counts.to_dict()
        
        # Sentiment distribution for gold (primary focus)
        gold_sentiment_counts = df['gold_sentiment'].value_counts()
        report['sentiment_distribution']['gold'] = gold_sentiment_counts.to_dict()
        
        # USD sentiment distribution
        usd_sentiment_counts = df['usd_sentiment'].value_counts()
        report['sentiment_distribution']['usd'] = usd_sentiment_counts.to_dict()
        
        # Confidence statistics
        report['confidence_stats'] = {
            'mean_confidence': df['sentiment_confidence'].mean(),
            'median_confidence': df['sentiment_confidence'].median(),
            'std_confidence': df['sentiment_confidence'].std(),
            'high_confidence_events': len(df[df['sentiment_confidence'] > 0.7]),
            'low_confidence_events': len(df[df['sentiment_confidence'] < 0.3])
        }
        
        # Category breakdown
        category_counts = df['sentiment_category'].value_counts()
        report['category_breakdown'] = category_counts.to_dict()
        
        return report


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Economic Event Sentiment Labeler')
    parser.add_argument('--input', '-i', required=True, 
                       help='Input CSV file with consolidated events')
    parser.add_argument('--output', '-o', 
                       help='Output CSV file for labeled events')
    parser.add_argument('--config', '-c', default='config/sentiment_rules.yaml',
                       help='Configuration file path')
    parser.add_argument('--report', '-r', 
                       help='Generate summary report to file')
    
    args = parser.parse_args()
    
    # Initialize labeler
    labeler = SentimentLabeler(config_path=args.config)
    
    # Process dataset
    if not args.output:
        # Use standardized output filename
        input_path = Path(args.input)
        args.output = str(input_path.parent / "economic_events_labeled.csv")
    
    # Run processing
    labeled_df = labeler.process_dataset(args.input, args.output)
    
    # Generate and save report
    if args.report:
        report = labeler.generate_summary_report(labeled_df)

        with open(args.report, 'w') as f:
            yaml.dump(report, f, default_flow_style=False, sort_keys=False)

        print(f"Summary report saved to: {args.report}")
    else:
        # Generate default report
        input_path = Path(args.input)
        default_report = str(input_path.parent / "sentiment_analysis_report.yaml")
        report = labeler.generate_summary_report(labeled_df)

        with open(default_report, 'w') as f:
            yaml.dump(report, f, default_flow_style=False, sort_keys=False)

        print(f"Summary report saved to: {default_report}")
    
    print(f"Sentiment labeling complete!")
    print(f"Labeled dataset: {args.output}")
    print(f"Total events processed: {len(labeled_df)}")


if __name__ == "__main__":
    main() 