#!/usr/bin/env python3
"""
Batch ForexFactory Economic Calendar Scraper

Systematically scrapes multiple weeks of economic data with:
- Progress tracking and resume capability
- Error handling and retry logic
- Rate limiting between weeks
- Data validation and deduplication
"""

import os
import sys
import time
import yaml
import logging
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Set
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from scripts.scraper import ForexFactoryScraper


class BatchForexFactoryScraper:
    """Batch scraper for collecting multiple weeks of ForexFactory data."""
    
    def __init__(self, config_path: str = 'config/settings.yaml'):
        """Initialize batch scraper."""
        self.config_path = config_path
        self.config = self._load_config()
        self.logger = self._setup_logging()
        self.progress_file = 'data/news/cache/batch_progress.yaml'
        self.scraped_weeks: Set[str] = set()
        self._load_progress()
        
    def _load_config(self) -> Dict:
        """Load configuration."""
        with open(self.config_path, 'r') as f:
            return yaml.safe_load(f)
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for batch operations."""
        logger = logging.getLogger('BatchForexFactoryScraper')
        logger.setLevel(logging.INFO)
        
        # Console handler
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _load_progress(self):
        """Load previous scraping progress."""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r') as f:
                    progress = yaml.safe_load(f) or {}
                self.scraped_weeks = set(progress.get('completed_weeks', []))
                self.logger.info(f"Loaded progress: {len(self.scraped_weeks)} weeks already scraped")
            except Exception as e:
                self.logger.warning(f"Could not load progress: {e}")
                self.scraped_weeks = set()
    
    def _save_progress(self, week: str):
        """Save scraping progress."""
        self.scraped_weeks.add(week)
        
        # Ensure cache directory exists
        os.makedirs(os.path.dirname(self.progress_file), exist_ok=True)
        
        progress = {
            'completed_weeks': list(self.scraped_weeks),
            'last_updated': datetime.now().isoformat(),
            'total_weeks': len(self.scraped_weeks)
        }
        
        with open(self.progress_file, 'w') as f:
            yaml.dump(progress, f, default_flow_style=False)
    
    def generate_week_list(self, start_date: str, end_date: str) -> List[str]:
        """
        Generate list of ForexFactory week identifiers.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            
        Returns:
            List of week identifiers (e.g., ['jan1.2024', 'jan8.2024', ...])
        """
        weeks = []
        
        # Parse dates
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        
        # Find first Monday (ForexFactory weeks start on Monday)
        current = start
        while current.weekday() != 0:  # 0 = Monday
            current -= timedelta(days=1)
        
        # Generate weekly identifiers
        while current <= end:
            month_name = current.strftime('%b').lower()  # jan, feb, etc.
            day = current.day
            year = current.year
            week_id = f"{month_name}{day}.{year}"
            weeks.append(week_id)
            current += timedelta(weeks=1)
        
        return weeks
    
    def scrape_week(self, week: str) -> bool:
        """
        Scrape a single week with error handling.
        
        Args:
            week: Week identifier (e.g., 'jan1.2024')
            
        Returns:
            True if successful, False otherwise
        """
        if week in self.scraped_weeks:
            self.logger.info(f"Week {week} already scraped, skipping")
            return True
        
        try:
            self.logger.info(f"Scraping week: {week}")
            
            # Initialize scraper
            scraper = ForexFactoryScraper(self.config_path)
            
            # Scrape data
            events = scraper.scrape_week(week)
            
            if events is not None and len(events) > 0:
                # Save the data to individual CSV file
                filename = scraper.save_data(events, week)
                self.logger.info(f"Successfully scraped {len(events)} events for week {week}")
                self.logger.info(f"Data saved to: {filename}")
                self._save_progress(week)
                return True
            else:
                self.logger.warning(f"No events found for week {week}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to scrape week {week}: {e}")
            return False
    
    def batch_scrape(self, weeks: List[str], delay_between_weeks: int = 5) -> Dict:
        """
        Scrape multiple weeks with progress tracking.
        
        Args:
            weeks: List of week identifiers
            delay_between_weeks: Delay in seconds between weeks
            
        Returns:
            Summary statistics
        """
        total_weeks = len(weeks)
        successful = 0
        failed = 0
        skipped = 0
        
        self.logger.info(f"Starting batch scrape of {total_weeks} weeks")
        
        for i, week in enumerate(weeks, 1):
            self.logger.info(f"Progress: {i}/{total_weeks} - Processing week {week}")
            
            if week in self.scraped_weeks:
                skipped += 1
                continue
            
            # Scrape week
            if self.scrape_week(week):
                successful += 1
            else:
                failed += 1
            
            # Rate limiting between weeks
            if i < total_weeks:  # Don't delay after last week
                self.logger.info(f"Waiting {delay_between_weeks} seconds before next week...")
                time.sleep(delay_between_weeks)
        
        # Summary
        summary = {
            'total_weeks': total_weeks,
            'successful': successful,
            'failed': failed,
            'skipped': skipped,
            'completion_rate': f"{((successful + skipped) / total_weeks * 100):.1f}%"
        }
        
        self.logger.info("Batch scraping complete!")
        self.logger.info(f"Summary: {summary}")
        
        return summary
    
    def get_scraped_files(self) -> List[str]:
        """Get list of all scraped CSV files."""
        raw_dir = Path('data/news/raw')
        if not raw_dir.exists():
            return []
        
        csv_files = list(raw_dir.glob('forexfactory_*.csv'))
        return [str(f) for f in sorted(csv_files)]
    
    def consolidate_data(self, output_file: str | None = None,
                        detailed_output: str | None = None) -> tuple[str | None, str | None]:
        """
        Consolidate all scraped CSV files into one master dataset.

        Args:
            output_file: Output file path for main dataset (optional)
            detailed_output: Output file path for detailed dataset (optional)

        Returns:
            Tuple of (main_file_path, detailed_file_path)
        """
        if output_file is None:
            output_file = 'data/news/processed/economic_events_raw.csv'

        if detailed_output is None:
            detailed_output = 'data/news/processed/economic_events_detailed.csv'
        
        # Ensure processed directory exists
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        csv_files = self.get_scraped_files()
        
        if not csv_files:
            self.logger.warning("No CSV files found to consolidate")
            return None
        
        self.logger.info(f"Consolidating {len(csv_files)} CSV files")
        
        # Load and combine all CSV files
        all_events = []
        
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file)
                all_events.append(df)
                self.logger.debug(f"Loaded {len(df)} events from {csv_file}")
            except Exception as e:
                self.logger.error(f"Error loading {csv_file}: {e}")
        
        if not all_events:
            self.logger.error("No data loaded from CSV files")
            return None
        
        # Combine all dataframes
        consolidated_df = pd.concat(all_events, ignore_index=True)
        
        # Remove duplicates (same timestamp, currency, event)
        initial_count = len(consolidated_df)
        consolidated_df = consolidated_df.drop_duplicates(
            subset=['timestamp', 'currency', 'event'], 
            keep='first'
        )
        final_count = len(consolidated_df)
        
        # Sort by timestamp
        consolidated_df['timestamp'] = pd.to_datetime(consolidated_df['timestamp'])
        consolidated_df = consolidated_df.sort_values('timestamp').reset_index(drop=True)
        
        # Save main consolidated data (clean columns only)
        main_columns = ['timestamp', 'currency', 'event', 'impact', 'actual', 'forecast', 'previous', 'scraped_at']
        main_df = consolidated_df[main_columns].copy()
        main_df.to_csv(output_file, index=False)

        # Save detailed data (all columns including metadata)
        consolidated_df.to_csv(detailed_output, index=False)

        self.logger.info(f"Consolidated {initial_count} events → {final_count} unique events")
        self.logger.info(f"Removed {initial_count - final_count} duplicates")
        self.logger.info(f"Main dataset saved to: {output_file}")
        self.logger.info(f"Detailed dataset saved to: {detailed_output}")

        return output_file, detailed_output


def main():
    """Main function for batch scraping."""
    parser = argparse.ArgumentParser(description='Batch ForexFactory Economic Calendar Scraper')
    parser.add_argument('--start-date', required=True, help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', required=True, help='End date (YYYY-MM-DD)')
    parser.add_argument('--delay', type=int, default=5, help='Delay between weeks (seconds)')
    parser.add_argument('--config', default='config/settings.yaml', help='Configuration file')
    parser.add_argument('--consolidate', action='store_true', help='Consolidate data after scraping')
    
    args = parser.parse_args()
    
    try:
        # Initialize batch scraper
        batch_scraper = BatchForexFactoryScraper(args.config)
        
        # Generate week list
        weeks = batch_scraper.generate_week_list(args.start_date, args.end_date)
        print(f"Generated {len(weeks)} weeks to scrape: {weeks[:5]}...")
        
        # Batch scrape
        summary = batch_scraper.batch_scrape(weeks, args.delay)
        
        # Consolidate data if requested
        if args.consolidate:
            print("\nConsolidating data...")
            main_file, detailed_file = batch_scraper.consolidate_data()
            if main_file and detailed_file:
                print(f"Main dataset saved to: {main_file}")
                print(f"Detailed dataset saved to: {detailed_file}")
        
        print(f"\nBatch scraping complete: {summary}")
        
    except KeyboardInterrupt:
        print("\nBatch scraping interrupted by user")
    except Exception as e:
        print(f"Error during batch scraping: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main() 